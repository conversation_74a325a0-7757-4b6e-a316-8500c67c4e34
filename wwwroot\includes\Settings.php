<?php
/**
 * 站点设置管理类
 *
 * 负责定义和管理站点设置的结构、默认值和验证规则
 * 这个类被所有三个子项目共享使用
 */
class Settings {
    /**
     * 站点设置文件路径
     */
    private static $settingsPath = null;

    /**
     * 站点设置缓存
     */
    private static $settings = null;

    /**
     * 设置站点设置文件路径
     *
     * @param string $path 站点设置文件路径
     */
    public static function setSettingsPath($path) {
        self::$settingsPath = $path;
        self::$settings = null; // 清除缓存
    }

    /**
     * 获取站点设置文件路径
     *
     * @return string 站点设置文件路径
     */
    public static function getSettingsPath() {
        if (self::$settingsPath === null) {
            // 默认路径
            self::$settingsPath = dirname(dirname(__FILE__)) . DIRECTORY_SEPARATOR . 'site_settings.php';
        }
        return self::$settingsPath;
    }

    /**
     * 加载站点设置
     *
     * @param bool $forceReload 是否强制重新加载
     * @return array 站点设置
     */
    public static function load($forceReload = false) {
        // 如果已经加载过且不需要强制重新加载，则直接返回缓存
        if (self::$settings !== null && !$forceReload) {
            return self::$settings;
        }

        $path = self::getSettingsPath();

        // 检查文件是否存在
        if (!file_exists($path)) {
            self::$settings = [];
            return [];
        }

        // 加载设置
        $settings = include $path;

        // 确保返回的是数组
        if (!is_array($settings)) {
            $settings = [];
        }

        // 缓存设置
        self::$settings = $settings;

        return $settings;
    }

    /**
     * 获取设置值
     *
     * @param string $path 设置路径，格式为 "group.key1.key2.key3"，如果只有 group 则返回整个组
     * @param mixed $default 默认值，如果设置不存在则返回此值
     * @return mixed 设置值
     */
    public static function get($path, $default = null) {
        $settings = self::load();

        // 如果路径为空，返回所有设置
        if (empty($path)) {
            return $settings;
        }

        // 解析路径
        $parts = explode('.', $path);
        $value = $settings;

        // 遍历路径各部分
        foreach ($parts as $part) {
            if (!isset($value[$part])) {
                return $default;
            }
            $value = $value[$part];
        }

        return $value;
    }

    /**
     * 向后兼容的获取设置值方法
     *
     * @param string $group 设置组名称
     * @param string $key 设置键名，如果为null则返回整个组
     * @param mixed $default 默认值，如果设置不存在则返回此值
     * @return mixed 设置值
     * @deprecated 使用 get($path) 代替
     */
    public static function getOld($group, $key = null, $default = null) {
        $settings = self::load();

        // 处理特殊的根级别设置
        if ($group === '_root') {
            if ($key === null) {
                // 返回所有根级别设置
                $rootSettings = [];
                foreach ($settings as $k => $v) {
                    if (!is_array($v)) {
                        $rootSettings[$k] = $v;
                    }
                }
                return $rootSettings;
            }

            // 返回特定的根级别设置
            return isset($settings[$key]) && !is_array($settings[$key]) ? $settings[$key] : $default;
        }

        // 如果组不存在，返回默认值
        if (!isset($settings[$group])) {
            return $default;
        }

        // 如果key为null，返回整个组
        if ($key === null) {
            return $settings[$group];
        }

        // 处理嵌套键名，如 wechat_pay.enabled
        if (strpos($key, '.') !== false) {
            $parts = explode('.', $key);
            $value = $settings[$group];

            foreach ($parts as $part) {
                if (!isset($value[$part])) {
                    return $default;
                }
                $value = $value[$part];
            }

            return $value;
        }

        // 如果key不存在，返回默认值
        if (!isset($settings[$group][$key])) {
            return $default;
        }

        return $settings[$group][$key];
    }

    /**
     * 获取所有设置的默认值
     *
     * @return array 所有设置的默认值
     */
    public static function getDefaults() {
        return [
            // 基本设置
            'basic' => [
                'site_title' => '软件下载站',
                'site_subtitle' => '提供优质软件下载服务',
                'site_description' => '这是一个提供各类软件下载的网站，所有软件均经过安全检测。',
                'site_keywords' => '软件下载,免费软件,安全软件',
                'site_logo' => '',
                'site_favicon' => '',
                'qr_code_url' => '',
                'frontend_url' => '',
            ],

            // 公告设置
            'announcement' => [
                'enabled' => false,
                'title' => '网站公告',
                'content' => '欢迎访问本站！',
                'style' => 'info',
                'repeat_show' => false,
                'timestamp' => 0,
            ],

            // 搜索设置
            'search' => [
                'mode' => 'instant', // instant 或 click
                'min_length' => 2,
                'placeholder' => '搜索软件...',
                'delay' => 300, // 即时搜索的延迟时间（毫秒）
            ],

            // 分页设置
            'pagination' => [
                'default_page_size' => 10,
                'max_page_size' => 50,
                'show_total' => true,
            ],

            // 支付设置
            'payment' => [
                'enabled' => false,
                'button_layout' => 'horizontal',
                'wechat_pay' => [
                    'enabled' => false,
                    'app_id' => '',
                    'mch_id' => '',
                    'key' => '',
                    'cert_path' => '',
                    'key_path' => '',
                    'notify_url' => 'api/public/payment_notify.php?type=wechat_pay',
                    'gateway' => 'https://api.mch.weixin.qq.com/pay/unifiedorder'
                ],
                'alipay' => [
                    'enabled' => false,
                    'app_id' => '',
                    'private_key' => '',
                    'public_key' => '',
                    'notify_url' => 'api/public/payment_notify.php?type=alipay',
                    'gateway' => 'https://openapi.alipay.com/gateway.do'
                ],
            ],

            // 上传设置
            'upload' => [
                'software_package_path' => 'uploads/software/package/',
                'software_icon_path' => 'uploads/software/icon/',
                'site_images_path' => 'uploads/site/',
                'max_size' => [
                    'software' => 524288000, // 500MB
                    'icon' => 2097152,       // 2MB
                    'site' => 5242880        // 5MB
                ],
                'allowed_extensions' => [
                    'software' => ['zip', 'rar', '7z', 'exe', 'msi', 'iso'],
                    'icon' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
                    'site' => ['jpg', 'jpeg', 'png', 'gif', 'webp']
                ]
            ],

            // 设备跳转设置
            'device_redirect' => [
                'enabled' => false,
                'ios_phone' => '',
                'ios_tablet' => '',
                'android_phone' => '',
                'android_tablet' => '',
                'android_desktop' => '',
                'windows_phone' => '',
                'windows_tablet' => '',
                'windows_desktop' => '',
                'macos_desktop' => '',
                'linux_desktop' => '',
                'unknown' => ''
            ],

            // 调试设置
            'debug' => [
                'enabled' => false,
                'skip_payment_signature_verification' => false,
                'log_payment_callbacks' => false
            ],
        ];
    }

    /**
     * 获取指定设置组的默认值
     *
     * @param string $group 设置组名称
     * @return array|null 设置组的默认值，如果不存在则返回null
     */
    public static function getGroupDefaults($group) {
        $defaults = self::getDefaults();
        return isset($defaults[$group]) ? $defaults[$group] : null;
    }

    /**
     * 获取指定设置的默认值
     *
     * @param string $path 设置路径，格式为 "group.key1.key2.key3"
     * @return mixed 设置的默认值，如果不存在则返回null
     */
    public static function getDefault($path) {
        $defaults = self::getDefaults();

        // 如果路径为空，返回所有默认值
        if (empty($path)) {
            return $defaults;
        }

        // 解析路径
        $parts = explode('.', $path);
        $value = $defaults;

        // 遍历路径各部分
        foreach ($parts as $part) {
            if (!isset($value[$part])) {
                return null;
            }
            $value = $value[$part];
        }

        return $value;
    }

    /**
     * 向后兼容的获取默认值方法
     *
     * @param string $group 设置组名称
     * @param string $key 设置键名
     * @return mixed 设置的默认值，如果不存在则返回null
     * @deprecated 使用 getDefault($path) 代替
     */
    public static function getDefaultOld($group, $key) {
        $defaults = self::getDefaults();

        // 处理特殊的根级别设置
        if ($group === '_root') {
            return isset($defaults[$key]) && !is_array($defaults[$key]) ? $defaults[$key] : null;
        }

        // 处理嵌套键名，如 wechat_pay.enabled
        if (strpos($key, '.') !== false) {
            $parts = explode('.', $key);

            if (!isset($defaults[$group])) {
                return null;
            }

            $value = $defaults[$group];
            foreach ($parts as $part) {
                if (!isset($value[$part])) {
                    return null;
                }
                $value = $value[$part];
            }

            return $value;
        }

        // 处理普通键名
        if (isset($defaults[$group]) && isset($defaults[$group][$key])) {
            return $defaults[$group][$key];
        }

        return null;
    }

    /**
     * 验证器接口
     */
    private static $validators = null;

    /**
     * 获取验证器
     *
     * @return array 验证器数组
     */
    private static function getValidators() {
        if (self::$validators === null) {
            self::$validators = [];

            // 从配置文件中加载验证规则
            $validationRules = self::loadValidationRules();

            // 根据验证规则生成验证器
            foreach ($validationRules as $path => $rule) {
                $validator = self::createValidator($rule);
                if ($validator !== null) {
                    self::setNestedValidator(self::$validators, $path, $validator);
                }
            }
        }

        return self::$validators;
    }

    /**
     * 从配置文件中加载验证规则
     *
     * @return array 验证规则数组
     */
    private static function loadValidationRules() {
        $validationRules = [];

        // 获取配置文件目录
        $configDir = dirname(__DIR__) . DS . 'api' . DS . 'admin' . DS . 'config';

        // 包含选项卡加载器文件
        if (file_exists($configDir . DS . 'settings_tabs.php')) {
            require_once $configDir . DS . 'settings_tabs.php';

            // 加载所有选项卡配置
            $tabsConfig = load_settings_tabs($configDir);

            // 遍历所有选项卡和字段，从字段属性推断验证规则
            foreach ($tabsConfig as $tab) {
                if (isset($tab['groups'])) {
                    foreach ($tab['groups'] as $group) {
                        if (isset($group['fields'])) {
                            foreach ($group['fields'] as $field) {
                                if (isset($field['name'])) {
                                    $rule = self::inferValidationRule($field);
                                    if ($rule !== null) {
                                        $validationRules[$field['name']] = $rule;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return $validationRules;
    }

    /**
     * 从字段配置推断验证规则
     *
     * @param array $field 字段配置
     * @return array|null 验证规则
     */
    private static function inferValidationRule($field) {
        // 如果字段有required属性且为true，并且是text类型，则为required_string
        if (isset($field['required']) && $field['required'] && isset($field['type']) && $field['type'] === 'text') {
            return ['type' => 'required_string'];
        }

        // 如果字段类型是checkbox或radio且有valueType为boolean，则为boolean验证
        if (isset($field['type']) && in_array($field['type'], ['checkbox', 'radio'])) {
            if (isset($field['valueType']) && $field['valueType'] === 'boolean') {
                return ['type' => 'boolean'];
            }
            // 如果是checkbox但没有valueType，默认也是boolean
            if ($field['type'] === 'checkbox') {
                return ['type' => 'boolean'];
            }
        }

        // 如果字段类型是number且有min/max，则为numeric_range验证
        if (isset($field['type']) && $field['type'] === 'number') {
            $rule = ['type' => 'numeric_range'];
            if (isset($field['min'])) {
                $rule['min'] = $field['min'];
            }
            if (isset($field['max'])) {
                $rule['max'] = $field['max'];
            }
            if (isset($field['step'])) {
                $rule['step'] = $field['step'];
            }
            return $rule;
        }

        // 如果字段有options，则为enum验证
        if (isset($field['options']) && is_array($field['options'])) {
            $allowedValues = [];
            foreach ($field['options'] as $option) {
                if (isset($option['value'])) {
                    $allowedValues[] = $option['value'];
                }
            }
            if (!empty($allowedValues)) {
                return [
                    'type' => 'enum',
                    'allowed_values' => $allowedValues
                ];
            }
        }

        // 如果字段名包含path，则为path验证
        if (strpos($field['name'], 'path') !== false) {
            return ['type' => 'path'];
        }

        // 如果字段名包含extensions，则为array验证（用于文件扩展名）
        if (strpos($field['name'], 'extensions') !== false) {
            return ['type' => 'array'];
        }

        // 如果字段名包含max_size，则为numeric_range验证（最小值为0）
        if (strpos($field['name'], 'max_size') !== false) {
            return [
                'type' => 'numeric_range',
                'min' => 0
            ];
        }

        // 默认返回null，表示不需要特殊验证
        return null;
    }

    /**
     * 根据验证规则创建验证器函数
     *
     * @param array $rule 验证规则
     * @return callable|null 验证器函数
     */
    private static function createValidator($rule) {
        if (!is_array($rule) || !isset($rule['type'])) {
            return null;
        }

        switch ($rule['type']) {
            case 'boolean':
                return function($value) {
                    return self::validateBoolean($value);
                };

            case 'required_string':
                return function($value) {
                    return !empty($value) && is_string($value);
                };

            case 'numeric_range':
                $min = isset($rule['min']) ? $rule['min'] : null;
                $max = isset($rule['max']) ? $rule['max'] : null;
                $step = isset($rule['step']) ? $rule['step'] : null;
                return function($value) use ($min, $max, $step) {
                    if (!is_numeric($value)) {
                        return false;
                    }
                    if ($min !== null && $value < $min) {
                        return false;
                    }
                    if ($max !== null && $value > $max) {
                        return false;
                    }
                    // 验证步进值
                    if ($step !== null && $step > 0) {
                        // 如果有最小值，从最小值开始计算步进
                        $base = $min !== null ? $min : 0;
                        $remainder = fmod($value - $base, $step);
                        // 考虑浮点数精度问题，使用小的容差值
                        if (abs($remainder) > 1e-10 && abs($remainder - $step) > 1e-10) {
                            return false;
                        }
                    }
                    return true;
                };

            case 'enum':
                $allowedValues = isset($rule['allowed_values']) ? $rule['allowed_values'] : [];
                return function($value) use ($allowedValues) {
                    return in_array($value, $allowedValues);
                };

            case 'path':
                return function($value) {
                    return self::validatePath($value);
                };

            case 'array':
                return function($value) {
                    if (!is_array($value)) {
                        return false;
                    }
                    foreach ($value as $item) {
                        if (!is_array($item)) {
                            return false;
                        }
                    }
                    return true;
                };

            case 'payment_provider':
                $provider = isset($rule['provider']) ? $rule['provider'] : null;
                return function($value) use ($provider) {
                    return self::validatePaymentProvider($value, $provider);
                };

            default:
                return null;
        }
    }

    /**
     * 设置嵌套验证器
     *
     * @param array &$validators 验证器数组
     * @param string $path 设置路径
     * @param callable $validator 验证器函数
     */
    private static function setNestedValidator(&$validators, $path, $validator) {
        $parts = explode('.', $path);

        // 如果只有一个部分，可能是根级别设置
        if (count($parts) === 1) {
            $key = $parts[0];
            if (!isset($validators['_root'])) {
                $validators['_root'] = [];
            }
            $validators['_root'][$key] = $validator;
            return;
        }

        // 获取第一部分作为组
        $group = array_shift($parts);

        // 如果没有更多部分，无法设置验证器
        if (empty($parts)) {
            return;
        }

        // 获取下一部分作为键
        $key = array_shift($parts);

        // 如果没有更多部分，设置验证器
        if (empty($parts)) {
            if (!isset($validators[$group])) {
                $validators[$group] = [];
            }
            $validators[$group][$key] = $validator;
        } else {
            // 处理更深层次的嵌套
            $nestedKey = $key . '.' . implode('.', $parts);
            if (!isset($validators[$group])) {
                $validators[$group] = [];
            }
            $validators[$group][$nestedKey] = $validator;
        }
    }

    /**
     * 验证布尔值
     *
     * @param mixed $value 要验证的值
     * @return bool 是否为有效的布尔值
     */
    private static function validateBoolean($value) {
        return is_bool($value) || $value === 0 || $value === 1 || $value === '0' || $value === '1';
    }

    /**
     * 验证路径
     *
     * @param mixed $value 要验证的路径
     * @return bool 是否为有效的路径
     */
    private static function validatePath($value) {
        if (!is_string($value) || empty($value)) {
            return false;
        }
        // 确保路径以斜杠结尾
        return substr($value, -1) === '/' || substr($value, -1) === '\\';
    }

    /**
     * 验证支付提供商设置
     *
     * @param mixed $value 要验证的值
     * @param string $provider 支付提供商（wechat_pay 或 alipay）
     * @return bool 是否为有效的支付提供商设置
     */
    private static function validatePaymentProvider($value, $provider) {
        if (!is_array($value)) {
            return false;
        }

        // 如果未启用，不需要验证其他字段
        if (!isset($value['enabled']) || !$value['enabled']) {
            return true;
        }

        // 通用必填字段
        $requiredFields = ['app_id', 'notify_url'];

        // 特定提供商的必填字段
        if ($provider === 'wechat_pay') {
            $requiredFields[] = 'mch_id';
            $requiredFields[] = 'key';
        } else if ($provider === 'alipay') {
            $requiredFields[] = 'private_key';
            $requiredFields[] = 'public_key';
        }

        // 验证所有必填字段
        foreach ($requiredFields as $field) {
            if (!isset($value[$field]) || empty($value[$field])) {
                return false;
            }
        }

        return true;
    }

    /**
     * 验证设置值是否有效
     *
     * @param string $path 设置路径，格式为 "group.key1.key2.key3"
     * @param mixed $value 要验证的值
     * @return bool 值是否有效
     */
    public static function validateValue($path, $value) {
        $validators = self::getValidators();

        // 如果路径为空，无法验证
        if (empty($path)) {
            return false;
        }

        // 解析路径
        $parts = explode('.', $path);

        // 如果只有一个部分，可能是根级别设置
        if (count($parts) === 1) {
            $key = $parts[0];
            if (isset($validators['_root']) && isset($validators['_root'][$key])) {
                return $validators['_root'][$key]($value);
            }
        }

        // 获取第一部分作为组
        $group = array_shift($parts);

        // 如果没有更多部分，可能是组级别设置
        if (empty($parts)) {
            // 无法验证整个组
            return true;
        }

        // 获取下一部分作为键
        $key = array_shift($parts);

        // 如果没有更多部分，检查是否有对应的验证器
        if (empty($parts)) {
            if (isset($validators[$group]) && isset($validators[$group][$key])) {
                return $validators[$group][$key]($value);
            }
        } else {
            // 处理更深层次的嵌套
            // 这里可以根据需要添加更复杂的验证逻辑
            // 例如，可以递归查找验证器
            $nestedKey = $key . '.' . implode('.', $parts);
            if (isset($validators[$group]) && isset($validators[$group][$nestedKey])) {
                return $validators[$group][$nestedKey]($value);
            }
        }

        // 如果没有特定的验证器，默认返回true
        return true;
    }

    /**
     * 向后兼容的验证值方法
     *
     * @param string $group 设置组名称
     * @param string $key 设置键名
     * @param mixed $value 要验证的值
     * @return bool 值是否有效
     * @deprecated 使用 validateValue($path, $value) 代替
     */
    public static function validateValueOld($group, $key, $value) {
        $validators = self::getValidators();

        // 检查是否有对应的验证器
        if (isset($validators[$group]) && isset($validators[$group][$key])) {
            // 使用对应的验证器函数
            return $validators[$group][$key]($value);
        }

        // 如果没有特定的验证器，默认返回true
        return true;
    }

    /**
     * 将字符串转换为数组（用于处理allowed_extensions）
     *
     * @param string $str 逗号分隔的字符串
     * @return array 转换后的数组
     */
    public static function stringToArray($str) {
        if (empty($str)) {
            return [];
        }
        return array_map('trim', explode(',', $str));
    }

    /**
     * 将数组转换为字符串（用于显示allowed_extensions）
     *
     * @param array $arr 数组
     * @return string 逗号分隔的字符串
     */
    public static function arrayToString($arr) {
        if (!is_array($arr)) {
            return '';
        }
        return implode(',', $arr);
    }

    /**
     * 处理上传设置的特殊字段
     *
     * @param array $data 前端提交的数据
     * @return array 处理后的数据
     */
    public static function processUploadSettings($data) {
        // 处理上传大小限制
        if (isset($data['max_size_software'])) {
            if (!isset($data['max_size'])) {
                $data['max_size'] = [];
            }
            $data['max_size']['software'] = (int)$data['max_size_software'];
            unset($data['max_size_software']);
        }

        if (isset($data['max_size_icon'])) {
            if (!isset($data['max_size'])) {
                $data['max_size'] = [];
            }
            $data['max_size']['icon'] = (int)$data['max_size_icon'];
            unset($data['max_size_icon']);
        }

        if (isset($data['max_size_site'])) {
            if (!isset($data['max_size'])) {
                $data['max_size'] = [];
            }
            $data['max_size']['site'] = (int)$data['max_size_site'];
            unset($data['max_size_site']);
        }

        // 处理允许的文件扩展名
        if (isset($data['allowed_extensions_software'])) {
            if (!isset($data['allowed_extensions'])) {
                $data['allowed_extensions'] = [];
            }
            $data['allowed_extensions']['software'] = self::stringToArray($data['allowed_extensions_software']);
            unset($data['allowed_extensions_software']);
        }

        if (isset($data['allowed_extensions_icon'])) {
            if (!isset($data['allowed_extensions'])) {
                $data['allowed_extensions'] = [];
            }
            $data['allowed_extensions']['icon'] = self::stringToArray($data['allowed_extensions_icon']);
            unset($data['allowed_extensions_icon']);
        }

        if (isset($data['allowed_extensions_site'])) {
            if (!isset($data['allowed_extensions'])) {
                $data['allowed_extensions'] = [];
            }
            $data['allowed_extensions']['site'] = self::stringToArray($data['allowed_extensions_site']);
            unset($data['allowed_extensions_site']);
        }

        return $data;
    }

    /**
     * 设置指定路径的值
     *
     * @param string $path 设置路径，格式为 "group.key1.key2.key3"
     * @param mixed $value 要设置的值
     * @param bool $save 是否立即保存到文件
     * @return bool 是否设置成功
     */
    public static function set($path, $value, $save = true) {
        // 加载当前设置
        $settings = self::load(true);

        // 如果路径为空，直接替换整个设置
        if (empty($path)) {
            if (!is_array($value)) {
                return false;
            }
            self::$settings = $value;
            return $save ? self::saveToFile(self::$settings) : true;
        }

        // 解析路径
        $parts = explode('.', $path);

        // 使用引用来修改嵌套数组
        $current = &$settings;

        // 遍历路径各部分，除了最后一个
        $lastIndex = count($parts) - 1;
        for ($i = 0; $i < $lastIndex; $i++) {
            $part = $parts[$i];

            // 如果当前部分不存在或不是数组，创建一个空数组
            if (!isset($current[$part]) || !is_array($current[$part])) {
                $current[$part] = [];
            }

            // 移动引用到下一级
            $current = &$current[$part];
        }

        // 设置最后一个部分的值
        $current[$parts[$lastIndex]] = $value;

        // 更新缓存
        self::$settings = $settings;

        // 如果需要，保存到文件
        return $save ? self::saveToFile($settings) : true;
    }

    /**
     * 保存设置到文件
     *
     * @param array $settings 要保存的设置
     * @return bool 是否保存成功
     */
    private static function saveToFile($settings) {
        // 获取设置文件路径
        $path = self::getSettingsPath();

        // 创建备份
        if (file_exists($path)) {
            $backupPath = $path . '.bak.' . date('YmdHis');
            copy($path, $backupPath);
        }

        // 生成PHP代码
        $code = "<?php\n// 站点设置文件\n// 最后更新时间: " . date('Y-m-d H:i:s') . "\n\nreturn " . var_export($settings, true) . ";\n";

        // 保存到文件
        $result = file_put_contents($path, $code);

        return $result !== false;
    }

    /**
     * 保存设置
     *
     * @param array $settings 要保存的设置
     * @param bool $merge 是否与现有设置合并
     * @param bool $processUpload 是否处理上传设置
     * @return bool 是否保存成功
     */
    public static function save($settings, $merge = true, $processUpload = true) {
        // 处理上传设置
        if ($processUpload && isset($settings['upload'])) {
            $settings['upload'] = self::processUploadSettings($settings['upload']);
        }

        // 如果需要合并，先加载现有设置
        if ($merge) {
            $currentSettings = self::load(true);
            $settings = array_replace_recursive($currentSettings, $settings);
        }

        // 更新缓存
        self::$settings = $settings;

        // 保存到文件
        return self::saveToFile($settings);
    }

    /**
     * 检查站点是否已安装
     *
     * @return bool 是否已安装
     */
    public static function isInstalled() {
        $settings = self::load();

        // 检查必要的设置是否存在
        if (empty($settings)) {
            return false;
        }

        // 检查安装时间戳
        if (!isset($settings['installed_time']) || !$settings['installed_time']) {
            return false;
        }

        // 检查数据库路径
        if (!isset($settings['db_path']) || !$settings['db_path']) {
            return false;
        }

        // 检查数据库文件是否存在
        $dbPath = $settings['db_path'];
        if (!file_exists($dbPath)) {
            return false;
        }

        return true;
    }

    /**
     * 获取设置组中的所有键
     *
     * @param string $group 设置组名称
     * @return array 键名数组
     */
    public static function getKeys($group) {
        $settings = self::load();

        if ($group === '_root') {
            // 返回所有根级别设置的键
            $keys = [];
            foreach ($settings as $key => $value) {
                if (!is_array($value)) {
                    $keys[] = $key;
                }
            }
            return $keys;
        }

        if (!isset($settings[$group])) {
            return [];
        }

        return array_keys($settings[$group]);
    }

    /**
     * 获取设置组中的所有嵌套键
     *
     * @param string $group 设置组名称
     * @param string $prefix 键名前缀
     * @return array 嵌套键名数组
     */
    public static function getNestedKeys($group, $prefix = '') {
        $settings = self::load();
        $keys = [];

        if (!isset($settings[$group])) {
            return [];
        }

        foreach ($settings[$group] as $key => $value) {
            $currentKey = $prefix ? $prefix . '.' . $key : $key;

            if (is_array($value)) {
                // 递归获取嵌套键
                $nestedKeys = self::getNestedKeysFromArray($value, $currentKey);
                $keys = array_merge($keys, $nestedKeys);
                // 同时添加父键
                $keys[] = $currentKey;
            } else {
                $keys[] = $currentKey;
            }
        }

        return $keys;
    }

    /**
     * 从数组中获取所有嵌套键
     *
     * @param array $array 要处理的数组
     * @param string $prefix 键名前缀
     * @return array 嵌套键名数组
     */
    private static function getNestedKeysFromArray($array, $prefix = '') {
        $keys = [];

        foreach ($array as $key => $value) {
            $currentKey = $prefix ? $prefix . '.' . $key : $key;

            if (is_array($value)) {
                // 递归获取嵌套键
                $nestedKeys = self::getNestedKeysFromArray($value, $currentKey);
                $keys = array_merge($keys, $nestedKeys);
                // 同时添加父键
                $keys[] = $currentKey;
            } else {
                $keys[] = $currentKey;
            }
        }

        return $keys;
    }

    /**
     * 获取所有设置
     * @return array 所有设置
     */
    public static function getAll() {
        $settings = self::load();

        // 确保应用默认值
        return self::applyDefaultValues($settings);
    }

    /**
     * 获取当前设置（不应用默认值）
     * 直接从site_settings.php文件中读取的原始设置
     * @return array 当前设置
     */
    public static function getCurrentSettings() {
        return self::load(true);
    }

    /**
     * 应用默认值到设置
     * @param array $settings 当前设置
     * @return array 应用默认值后的设置
     */
    private static function applyDefaultValues($settings) {
        $defaults = self::getDefaults();

        // 递归合并默认值
        return self::mergeDefaults($settings, $defaults);
    }

    /**
     * 递归合并默认值
     * @param array $settings 当前设置
     * @param array $defaults 默认值
     * @return array 合并后的设置
     */
    private static function mergeDefaults($settings, $defaults) {
        $result = $settings;

        foreach ($defaults as $key => $value) {
            if (!isset($result[$key])) {
                $result[$key] = $value;
            } elseif (is_array($value) && is_array($result[$key])) {
                $result[$key] = self::mergeDefaults($result[$key], $value);
            }
        }

        return $result;
    }

    /**
     * 检查条件是否满足
     * @param string $condition 条件表达式
     * @param array $settings 当前设置
     * @return bool 条件是否满足
     */
    public static function checkCondition($condition, $settings = null) {
        // 如果没有提供设置，使用当前设置
        if ($settings === null) {
            $settings = self::load();
        }

        // 简单条件：字段名等于true
        if (strpos($condition, '===') === false && strpos($condition, '!==') === false) {
            $value = self::getNestedValue($settings, $condition);
            return !empty($value) && $value !== 'false';
        }

        // 复杂条件：字段名 === 值
        if (strpos($condition, '===') !== false) {
            list($field, $value) = explode('===', $condition);
            $field = trim($field);
            $value = trim($value);

            // 处理字符串值
            if ($value[0] === '"' || $value[0] === "'") {
                $value = substr($value, 1, -1);
            }

            $fieldValue = self::getNestedValue($settings, $field);
            return $fieldValue === $value;
        }

        // 复杂条件：字段名 !== 值
        if (strpos($condition, '!==') !== false) {
            list($field, $value) = explode('!==', $condition);
            $field = trim($field);
            $value = trim($value);

            // 处理字符串值
            if ($value[0] === '"' || $value[0] === "'") {
                $value = substr($value, 1, -1);
            }

            $fieldValue = self::getNestedValue($settings, $field);
            return $fieldValue !== $value;
        }

        return false;
    }

    /**
     * 获取嵌套值
     * @param array $array 数组
     * @param string $path 路径，如 'basic.site_title'
     * @return mixed 值
     */
    private static function getNestedValue($array, $path) {
        $keys = explode('.', $path);
        $value = $array;

        foreach ($keys as $key) {
            if (!isset($value[$key])) {
                return null;
            }
            $value = $value[$key];
        }

        return $value;
    }

    /**
     * 将嵌套结构转换为扁平结构
     * @param array $settings 嵌套结构的设置
     * @param string $prefix 前缀
     * @return array 扁平结构的设置
     */
    public static function flattenSettings($settings, $prefix = '') {
        $result = [];

        foreach ($settings as $key => $value) {
            $newKey = $prefix ? $prefix . '.' . $key : $key;

            if (is_array($value) && !empty($value) && !isset($value[0])) {
                // 递归处理嵌套数组
                $result = array_merge($result, self::flattenSettings($value, $newKey));
            } else {
                // 简单值或索引数组
                $result[$newKey] = $value;
            }
        }

        return $result;
    }

    /**
     * 将扁平结构转换为嵌套结构
     * @param array $settings 扁平结构的设置
     * @return array 嵌套结构的设置
     */
    public static function nestSettings($settings) {
        $result = [];

        foreach ($settings as $key => $value) {
            if (strpos($key, '.') !== false) {
                $keys = explode('.', $key);
                $current = &$result;

                foreach ($keys as $i => $k) {
                    if ($i === count($keys) - 1) {
                        $current[$k] = $value;
                    } else {
                        if (!isset($current[$k]) || !is_array($current[$k])) {
                            $current[$k] = [];
                        }
                        $current = &$current[$k];
                    }
                }
            } else {
                $result[$key] = $value;
            }
        }

        return $result;
    }

    /**
     * 验证设置
     * @param array $data 要验证的设置
     * @param array $tabsConfig 选项卡配置
     * @return array 验证错误
     */
    public static function validateSettings($data, $tabsConfig) {
        $errors = [];

        // 遍历所有选项卡
        foreach ($tabsConfig as $tab) {
            if (!isset($tab['groups'])) continue;

            // 遍历所有组
            foreach ($tab['groups'] as $group) {
                if (!isset($group['fields'])) continue;

                // 遍历所有字段
                foreach ($group['fields'] as $field) {
                    // 检查必填字段
                    if (isset($field['required']) && $field['required']) {
                        $fieldName = $field['name'];

                        // 检查字段是否存在且不为空
                        if (!isset($data[$fieldName]) || $data[$fieldName] === '') {
                            $errors[] = $field['label'] . '不能为空';
                        }
                    }

                    // 检查数字字段
                    if (isset($field['type']) && $field['type'] === 'number' && isset($data[$field['name']])) {
                        $value = $data[$field['name']];

                        // 检查最小值
                        if (isset($field['min']) && $value < $field['min']) {
                            $errors[] = $field['label'] . '不能小于' . $field['min'];
                        }

                        // 检查最大值
                        if (isset($field['max']) && $value > $field['max']) {
                            $errors[] = $field['label'] . '不能大于' . $field['max'];
                        }
                    }
                }
            }
        }

        return $errors;
    }

    /**
     * 获取支付配置
     *
     * @param string $type 支付类型（wechat_pay 或 alipay），如果为null则返回整个支付配置
     * @return array|null 支付配置
     */
    public static function getPaymentConfig($type = null) {
        // 获取完整的设置，包含默认值
        $settings = self::getAll();

        // 如果支付功能未启用，返回null
        if (!isset($settings['payment']) || !isset($settings['payment']['enabled']) || !$settings['payment']['enabled']) {
            return null;
        }

        // 获取前台URL
        $frontendUrl = isset($settings['basic']['frontend_url']) ? $settings['basic']['frontend_url'] : '';

        // 如果未指定类型，处理整个支付配置中的notify_url
        if ($type === null) {
            $paymentConfig = $settings['payment'];

            // 处理微信支付的notify_url
            if (isset($paymentConfig['wechat_pay']) && !empty($paymentConfig['wechat_pay']['notify_url'])) {
                // 如果notify_url是相对路径，添加frontend_url
                if (!empty($frontendUrl) && !preg_match('/^(https?:)?\/\//', $paymentConfig['wechat_pay']['notify_url'])) {
                    $paymentConfig['wechat_pay']['notify_url'] = $frontendUrl . '/' . ltrim($paymentConfig['wechat_pay']['notify_url'], '/');
                }
            }

            // 处理支付宝的notify_url
            if (isset($paymentConfig['alipay']) && !empty($paymentConfig['alipay']['notify_url'])) {
                // 如果notify_url是相对路径，添加frontend_url
                if (!empty($frontendUrl) && !preg_match('/^(https?:)?\/\//', $paymentConfig['alipay']['notify_url'])) {
                    $paymentConfig['alipay']['notify_url'] = $frontendUrl . '/' . ltrim($paymentConfig['alipay']['notify_url'], '/');
                }
            }

            return $paymentConfig;
        }

        // 检查指定的支付类型是否启用
        if (!isset($settings['payment'][$type]) || !isset($settings['payment'][$type]['enabled']) || !$settings['payment'][$type]['enabled']) {
            return null;
        }

        $typeConfig = $settings['payment'][$type];

        // 处理notify_url
        if (!empty($typeConfig['notify_url']) && !empty($frontendUrl)) {
            // 如果notify_url是相对路径，添加frontend_url
            if (!preg_match('/^(https?:)?\/\//', $typeConfig['notify_url'])) {
                $typeConfig['notify_url'] = $frontendUrl . '/' . ltrim($typeConfig['notify_url'], '/');
            }
        }

        return $typeConfig;
    }
}
