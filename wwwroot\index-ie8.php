<?php
// 包含安装检查
$frontendConfig = require_once __DIR__ . '/common.php';

// 加载站点设置
$settings = get_settings();

// 设置默认值 - 从basic组中获取配置
$siteTitle = $settings['basic']['site_title'] ?? '娜宝贝软件';
$siteSubtitle = $settings['basic']['site_subtitle'] ?? '探索未来科技的软件集合';

// 获取分页配置、搜索配置和调试模式
$defaultPageSize = 10;
$maxPageSize = 100;
$showTotal = true;
$debugEnabled = false;
$searchMode = 'click';
$searchDelay = 500;

if (isset($settings['pagination'])) {
    if (isset($settings['pagination']['default_page_size'])) {
        $defaultPageSize = (int)$settings['pagination']['default_page_size'];
        if ($defaultPageSize < 1) {
            $defaultPageSize = 1;
        }
    }

    if (isset($settings['pagination']['max_page_size'])) {
        $maxPageSize = (int)$settings['pagination']['max_page_size'];
        if ($maxPageSize < 0) {
            $maxPageSize = 1;
        }
    }

    if (isset($settings['pagination']['show_total'])) {
        $showTotal = (bool)$settings['pagination']['show_total'];
    }
}

// 获取调试模式设置
if (isset($frontendConfig['debug'])) {
    $debugEnabled = (bool)$frontendConfig['debug'];
}

// 获取搜索配置
if (isset($settings['search'])) {
    if (isset($settings['search']['mode'])) {
        $searchMode = $settings['search']['mode'];
    }

    if (isset($settings['search']['delay'])) {
        $searchDelay = (int)$settings['search']['delay'];
    }
}

// 检查公告配置是否存在
$announcementEnabled = isset($settings['announcement']) && isset($settings['announcement']['enabled']) && $settings['announcement']['enabled'];
$announcementTitle = $settings['announcement']['title'] ?? '重要公告';
$announcementContent = $settings['announcement']['content'] ?? '欢迎来到娜宝贝软件官网！我们秉着良好的态度和服务，让我的客户优先享用最好用的下载服务，极速的下载体验，让您感受不一样的下载方式！';
$announcementStyle = $settings['announcement']['style'] ?? 'default';
$announcementRepeatShow = $settings['announcement']['repeat_show'] ?? false;
$announcementTimestamp = $settings['announcement']['timestamp'] ?? time();

// 根据样式设置颜色类
$styleClasses = [
    'default' => [
        'bg' => 'linear-gradient(135deg, #001133, #003366)',
        'border' => '#00aaff',
        'title' => '#00ffff'
    ],
    'info' => [
        'bg' => 'linear-gradient(135deg, #001a33, #003366)',
        'border' => '#0088ff',
        'title' => '#00ccff'
    ],
    'success' => [
        'bg' => 'linear-gradient(135deg, #003322, #005533)',
        'border' => '#00cc88',
        'title' => '#00ffaa'
    ],
    'warning' => [
        'bg' => 'linear-gradient(135deg, #332200, #553300)',
        'border' => '#ffaa00',
        'title' => '#ffcc00'
    ],
    'danger' => [
        'bg' => 'linear-gradient(135deg, #330000, #550000)',
        'border' => '#ff0000',
        'title' => '#ff5555'
    ]
];

// 获取当前样式
$currentStyle = $styleClasses[$announcementStyle] ?? $styleClasses['default'];

// 不再检查cookie，使用localStorage在前端处理
?>
<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <link rel="icon" type="img/ji-ico" href="./img/ji.ico">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=8">
    <title><?php echo htmlspecialchars($siteTitle); ?> - 高速下载</title>
    
    <!-- IE8 兼容性支持 -->
    <!--[if lt IE 9]>
    <script src="https://cdn.jsdelivr.net/npm/html5shiv@3.7.3/dist/html5shiv.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/respond.js@1.4.2/dest/respond.min.js"></script>
    <![endif]-->
    
    <link href="<?php echo $frontendConfig['assets_path']; ?>libs/font-awesome/css/all.min.css" rel="stylesheet">
    <script src="<?php echo $frontendConfig['assets_path']; ?>libs/md5/blueimp-md5.min.js"></script>
    <style>
        /* IE8 兼容的全局样式 */
        * {
            margin: 0;
            padding: 0;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: #0a0a1a;
            color: #e0e0ff;
            min-height: 100vh;
            /* IE8 不支持 radial-gradient，使用纯色背景 */
            background: #0a0a1a;
        }

        /* 头部样式 - IE8 兼容 */
        header {
            background: #001133; /* IE8 fallback */
            background: -webkit-gradient(linear, left top, right top, from(#001133), to(#003366));
            background: -webkit-linear-gradient(left, #001133, #003366);
            background: -moz-linear-gradient(left, #001133, #003366);
            background: -o-linear-gradient(left, #001133, #003366);
            background: linear-gradient(to right, #001133, #003366);
            padding: 16px;
            text-align: center;
            border-bottom: 2px solid #00aaff;
            position: relative;
            overflow: hidden;
            /* IE8 发光效果 */
            filter: progid:DXImageTransform.Microsoft.DropShadow(color=#3300aaff, offx=0, offy=0);
        }

        h1 {
            font-size: 28px;
            color: #00ffff;
            letter-spacing: 1px;
            margin-bottom: 8px;
            /* IE8 文字阴影效果 */
            filter: progid:DXImageTransform.Microsoft.DropShadow(color=#0066ff, offx=1, offy=1);
        }

        .subtitle {
            color: #aaccff;
            font-size: 14px;
            letter-spacing: 0.5px;
        }

        /* 主要内容区 - IE8 兼容布局 */
        .container {
            width: 1200px;
            max-width: 100%;
            margin: 16px auto;
            padding: 0 16px;
            /* IE8 使用 float 布局 */
        }

        /* 左侧分类导航 - IE8 兼容 */
        .sidebar {
            width: 250px;
            float: left;
            background: rgba(10, 20, 40, 0.7);
            background: #0a1428; /* IE8 fallback */
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            margin-right: 24px;
            border: 1px solid #334466;
            /* IE8 发光效果 */
            filter: progid:DXImageTransform.Microsoft.DropShadow(color=#330064ff, offx=0, offy=0);
        }

        .sidebar h2 {
            color: #00ccff;
            font-size: 19px;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid #334477;
            /* IE8 文字阴影 */
            filter: progid:DXImageTransform.Microsoft.DropShadow(color=#0066ff, offx=1, offy=1);
        }

        .category-list {
            list-style: none;
        }

        .category-item {
            margin-bottom: 4px;
            position: relative;
        }

        .category-item a {
            color: #aaccff;
            text-decoration: none;
            display: block;
            padding: 8px;
            border-radius: 4px;
            position: relative;
            font-size: 14px;
        }

        .category-item a:hover {
            background: rgba(0, 100, 255, 0.2);
            background: #001a66; /* IE8 fallback */
            color: #00ffff;
        }

        .category-item.active a {
            background: rgba(0, 100, 255, 0.3);
            background: #002080; /* IE8 fallback */
            color: #00ffff;
            border-left: 3px solid #00aaff;
        }

        .category-badge {
            background: rgba(0, 170, 255, 0.3);
            background: #002080; /* IE8 fallback */
            color: #00ffff;
            padding: 2px 6px;
            border-radius: 999px;
            font-size: 11px;
            float: right;
        }

        /* 右侧内容区 - IE8 兼容 */
        .main-content {
            margin-left: 274px; /* sidebar width + margin */
        }

        /* 顶部搜索框 - IE8 兼容 */
        .top-search {
            margin-bottom: 16px;
            position: relative;
            width: 100%;
            height: 50px;
            background: rgba(10, 20, 40, 0.7);
            background: #0a1428; /* IE8 fallback */
            border-radius: 8px;
            border: 1px solid #334466;
            padding: 8px;
            overflow: hidden;
            /* IE8 发光效果 */
            filter: progid:DXImageTransform.Microsoft.DropShadow(color=#330064ff, offx=0, offy=0);
        }

        .top-search input {
            width: 100%;
            height: 100%;
            padding: 0 16px;
            background: transparent;
            border: none;
            color: #e0e0ff;
            outline: none;
            font-size: 16px;
        }

        /* 点击搜索模式下的输入框样式 */
        .click-search input {
            padding-right: 96px;
        }

        .top-search button {
            position: absolute;
            right: 8px;
            top: 50%;
            margin-top: -17px; /* IE8 不支持 transform，使用 margin */
            background: #00aaff; /* IE8 fallback */
            background: -webkit-gradient(linear, left top, right bottom, from(#00aaff), to(#0066ff));
            background: -webkit-linear-gradient(135deg, #00aaff, #0066ff);
            background: -moz-linear-gradient(135deg, #00aaff, #0066ff);
            background: -o-linear-gradient(135deg, #00aaff, #0066ff);
            background: linear-gradient(135deg, #00aaff, #0066ff);
            color: #ffffff;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 14px;
            /* IE8 发光效果 */
            filter: progid:DXImageTransform.Microsoft.DropShadow(color=#3300aaff, offx=0, offy=0);
        }

        .top-search button:hover {
            background: #00ccff; /* IE8 fallback */
            background: -webkit-gradient(linear, left top, right bottom, from(#00ccff), to(#0088ff));
            background: -webkit-linear-gradient(135deg, #00ccff, #0088ff);
            background: -moz-linear-gradient(135deg, #00ccff, #0088ff);
            background: -o-linear-gradient(135deg, #00ccff, #0088ff);
            background: linear-gradient(135deg, #00ccff, #0088ff);
        }

        /* 软件列表 - IE8 兼容 */
        .software-list {
            /* IE8 使用普通布局 */
        }

        .software-card {
            background: rgba(20, 30, 60, 0.6);
            background: #141e3c; /* IE8 fallback */
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #445588;
            position: relative;
            overflow: hidden;
            margin-bottom: 16px;
            /* IE8 使用 zoom 实现缩放效果 */
            zoom: 1;
        }

        .software-card:hover {
            /* IE8 不支持 transform，使用 zoom */
            zoom: 1.02;
            border-color: #00aaff;
            /* IE8 发光效果 */
            filter: progid:DXImageTransform.Microsoft.DropShadow(color=#330064ff, offx=0, offy=2);
        }

        /* 分类徽章 - IE8 兼容 */
        .software-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-bottom: 8px;
        }

        .badge-dev {
            background: rgba(0, 170, 255, 0.2);
            background: #002080; /* IE8 fallback */
            color: #00aaff;
        }

        .badge-design {
            background: rgba(170, 0, 255, 0.2);
            background: #2a0040; /* IE8 fallback */
            color: #aa00ff;
        }

        .badge-security {
            background: rgba(0, 255, 170, 0.2);
            background: #004020; /* IE8 fallback */
            color: #00ffaa;
        }

        .badge-browser {
            background: rgba(255, 170, 0, 0.2);
            background: #402a00; /* IE8 fallback */
            color: #ffaa00;
        }

        .badge-system {
            background: rgba(255, 0, 170, 0.2);
            background: #400020; /* IE8 fallback */
            color: #ff00aa;
        }

        /* 图标区域 - IE8 兼容 */
        .software-icon-container {
            width: 60px;
            height: 60px;
            float: left;
            margin-right: 16px;
            margin-bottom: 8px;
            background: rgba(0, 100, 255, 0.1);
            background: #001a33; /* IE8 fallback */
            border-radius: 8px;
            border: 1px solid #334477;
            position: relative;
            overflow: hidden;
            text-align: center;
            line-height: 60px;
        }

        .software-icon {
            width: 40px;
            height: 40px;
            vertical-align: middle;
        }

        /* 内容区域 - IE8 兼容 */
        .software-content {
            margin-left: 76px; /* icon width + margin */
            margin-right: 150px; /* actions width */
        }

        .software-content h3 {
            color: #00ccff;
            font-size: 17px;
            margin-bottom: 8px;
        }

        .software-description {
            color: #aaccff;
            font-size: 13px;
            line-height: 1.4;
            margin-bottom: 12px;
        }

        .software-meta {
            color: #7788aa;
            font-size: 12px;
            margin-bottom: 12px;
        }

        .software-meta span {
            margin-right: 12px;
        }

        /* 下载按钮区域 - IE8 兼容 */
        .software-actions {
            width: 150px;
            float: right;
            margin-top: -60px; /* 对齐到顶部 */
        }

        .download-btn, .video-demo-btn, .baidu-disk-btn, .purchase-btn {
            display: block;
            color: #ffffff;
            padding: 10px 8px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: bold;
            text-align: center;
            border: none;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            font-size: 13px;
            margin-bottom: 8px;
            width: 100%;
        }

        .download-btn {
            background: #00aaff; /* IE8 fallback */
            background: -webkit-gradient(linear, left top, right bottom, from(#00aaff), to(#0066ff));
            background: -webkit-linear-gradient(135deg, #00aaff, #0066ff);
            background: -moz-linear-gradient(135deg, #00aaff, #0066ff);
            background: -o-linear-gradient(135deg, #00aaff, #0066ff);
            background: linear-gradient(135deg, #00aaff, #0066ff);
        }

        .download-btn:hover {
            background: #00ccff; /* IE8 fallback */
            background: -webkit-gradient(linear, left top, right bottom, from(#00ccff), to(#0088ff));
            background: -webkit-linear-gradient(135deg, #00ccff, #0088ff);
            background: -moz-linear-gradient(135deg, #00ccff, #0088ff);
            background: -o-linear-gradient(135deg, #00ccff, #0088ff);
            background: linear-gradient(135deg, #00ccff, #0088ff);
        }

        /* 视频演示按钮 */
        .video-demo-btn {
            background: #00aaff; /* IE8 fallback */
            background: -webkit-gradient(linear, left top, right bottom, from(#00aaff), to(#0066ff));
            background: -webkit-linear-gradient(135deg, #00aaff, #0066ff);
            background: -moz-linear-gradient(135deg, #00aaff, #0066ff);
            background: -o-linear-gradient(135deg, #00aaff, #0066ff);
            background: linear-gradient(135deg, #00aaff, #0066ff);
        }

        .video-demo-btn:hover {
            background: #00ccff; /* IE8 fallback */
            background: -webkit-gradient(linear, left top, right bottom, from(#00ccff), to(#0088ff));
            background: -webkit-linear-gradient(135deg, #00ccff, #0088ff);
            background: -moz-linear-gradient(135deg, #00ccff, #0088ff);
            background: -o-linear-gradient(135deg, #00ccff, #0088ff);
            background: linear-gradient(135deg, #00ccff, #0088ff);
        }

        /* 百度网盘下载按钮 */
        .baidu-disk-btn {
            background: #00aa77; /* IE8 fallback */
            background: -webkit-gradient(linear, left top, right bottom, from(#00aa77), to(#006644));
            background: -webkit-linear-gradient(135deg, #00aa77, #006644);
            background: -moz-linear-gradient(135deg, #00aa77, #006644);
            background: -o-linear-gradient(135deg, #00aa77, #006644);
            background: linear-gradient(135deg, #00aa77, #006644);
        }

        .baidu-disk-btn:hover {
            background: #00cc99; /* IE8 fallback */
            background: -webkit-gradient(linear, left top, right bottom, from(#00cc99), to(#008866));
            background: -webkit-linear-gradient(135deg, #00cc99, #008866);
            background: -moz-linear-gradient(135deg, #00cc99, #008866);
            background: -o-linear-gradient(135deg, #00cc99, #008866);
            background: linear-gradient(135deg, #00cc99, #008866);
        }

        /* 自助购买按钮 */
        .purchase-btn {
            background: #ff5500; /* IE8 fallback */
            background: -webkit-gradient(linear, left top, right bottom, from(#ff5500), to(#aa3300));
            background: -webkit-linear-gradient(135deg, #ff5500, #aa3300);
            background: -moz-linear-gradient(135deg, #ff5500, #aa3300);
            background: -o-linear-gradient(135deg, #ff5500, #aa3300);
            background: linear-gradient(135deg, #ff5500, #aa3300);
        }

        .purchase-btn:hover {
            background: #ff7722; /* IE8 fallback */
            background: -webkit-gradient(linear, left top, right bottom, from(#ff7722), to(#cc5500));
            background: -webkit-linear-gradient(135deg, #ff7722, #cc5500);
            background: -moz-linear-gradient(135deg, #ff7722, #cc5500);
            background: -o-linear-gradient(135deg, #ff7722, #cc5500);
            background: linear-gradient(135deg, #ff7722, #cc5500);
        }

        /* 页脚 - IE8 兼容 */
        footer {
            text-align: center;
            padding: 24px;
            color: #7788aa;
            font-size: 12px;
            border-top: 1px solid #223344;
            margin-top: 24px;
            clear: both;
        }

        /* 关于我们按钮样式 - IE8 兼容 */
        .about-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            margin-top: -17px; /* IE8 不支持 transform */
            background: rgba(0, 170, 255, 0.2);
            background: #002080; /* IE8 fallback */
            color: #00ccff;
            border: 1px solid #00aaff;
            border-radius: 4px;
            padding: 6px 12px;
            cursor: pointer;
            z-index: 10;
            font-size: 12px;
            /* IE8 发光效果 */
            filter: progid:DXImageTransform.Microsoft.DropShadow(color=#3300aaff, offx=0, offy=0);
        }

        .about-btn:hover {
            background: rgba(0, 170, 255, 0.4);
            background: #0033cc; /* IE8 fallback */
        }

        /* 弹窗样式 - IE8 兼容 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 30, 0.8);
            background: #00001e; /* IE8 fallback */
            z-index: 100;
        }

        .modal.active {
            display: block;
        }

        .modal-content {
            background: #001133; /* IE8 fallback */
            background: -webkit-gradient(linear, left top, right bottom, from(#001133), to(#003366));
            background: -webkit-linear-gradient(135deg, #001133, #003366);
            background: -moz-linear-gradient(135deg, #001133, #003366);
            background: -o-linear-gradient(135deg, #001133, #003366);
            background: linear-gradient(135deg, #001133, #003366);
            padding: 24px;
            border-radius: 10px;
            border: 1px solid #00aaff;
            text-align: center;
            position: absolute;
            top: 50%;
            left: 50%;
            margin-top: -150px; /* IE8 不支持 transform，使用固定 margin */
            margin-left: -250px;
            width: 500px;
            max-width: 90%;
            /* IE8 发光效果 */
            filter: progid:DXImageTransform.Microsoft.DropShadow(color=#8000aaff, offx=0, offy=0);
        }

        .modal-close {
            position: absolute;
            top: 10px;
            right: 10px;
            color: #00ffff;
            font-size: 19px;
            cursor: pointer;
        }

        .modal-close:hover {
            color: #ff0000;
        }

        .modal-title {
            color: #00ffff;
            margin-bottom: 16px;
            /* IE8 文字阴影 */
            filter: progid:DXImageTransform.Microsoft.DropShadow(color=#0066ff, offx=1, offy=1);
            font-size: 20px;
        }

        .modal-text {
            color: #aaccff;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 19px;
        }

        .modal-btn {
            display: inline-block;
            background: #00aaff; /* IE8 fallback */
            background: -webkit-gradient(linear, left top, right bottom, from(#00aaff), to(#0066ff));
            background: -webkit-linear-gradient(135deg, #00aaff, #0066ff);
            background: -moz-linear-gradient(135deg, #00aaff, #0066ff);
            background: -o-linear-gradient(135deg, #00aaff, #0066ff);
            background: linear-gradient(135deg, #00aaff, #0066ff);
            color: #ffffff;
            padding: 10px 19px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: bold;
            text-align: center;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }

        .modal-btn:hover {
            background: #00ccff; /* IE8 fallback */
            background: -webkit-gradient(linear, left top, right bottom, from(#00ccff), to(#0088ff));
            background: -webkit-linear-gradient(135deg, #00ccff, #0088ff);
            background: -moz-linear-gradient(135deg, #00ccff, #0088ff);
            background: -o-linear-gradient(135deg, #00ccff, #0088ff);
            background: linear-gradient(135deg, #00ccff, #0088ff);
        }

        /* 搜索结果为空提示 - IE8 兼容 */
        .search-empty {
            text-align: center;
            padding: 32px 0;
        }

        .search-empty h3 {
            color: #00ccff;
            font-size: 19px;
            margin-bottom: 12px;
        }

        .search-empty p {
            color: #aaccff;
            font-size: 14px;
        }

        /* 分页样式 - IE8 兼容 */
        .pagination-container {
            margin-top: 32px;
            padding: 16px;
            background: rgba(10, 20, 40, 0.7);
            background: #0a1428; /* IE8 fallback */
            border-radius: 8px;
            border: 1px solid #334466;
            overflow: hidden; /* IE8 清除浮动 */
        }

        .pagination-info {
            color: #aaccff;
            font-size: 14px;
            float: left;
            line-height: 34px;
        }

        .pagination-controls {
            float: right;
        }

        .pagination-btn {
            background: #00aaff; /* IE8 fallback */
            background: -webkit-gradient(linear, left top, right bottom, from(#00aaff), to(#0066ff));
            background: -webkit-linear-gradient(135deg, #00aaff, #0066ff);
            background: -moz-linear-gradient(135deg, #00aaff, #0066ff);
            background: -o-linear-gradient(135deg, #00aaff, #0066ff);
            background: linear-gradient(135deg, #00aaff, #0066ff);
            color: #ffffff;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            margin-left: 8px;
        }

        .pagination-btn:hover {
            background: #00ccff; /* IE8 fallback */
            background: -webkit-gradient(linear, left top, right bottom, from(#00ccff), to(#0088ff));
            background: -webkit-linear-gradient(135deg, #00ccff, #0088ff);
            background: -moz-linear-gradient(135deg, #00ccff, #0088ff);
            background: -o-linear-gradient(135deg, #00ccff, #0088ff);
            background: linear-gradient(135deg, #00ccff, #0088ff);
        }

        .pagination-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background: #336699; /* IE8 fallback */
            background: -webkit-gradient(linear, left top, right bottom, from(#336699), to(#003366));
            background: -webkit-linear-gradient(135deg, #336699, #003366);
            background: -moz-linear-gradient(135deg, #336699, #003366);
            background: -o-linear-gradient(135deg, #336699, #003366);
            background: linear-gradient(135deg, #336699, #003366);
        }

        .pagination-btn.disabled:hover {
            background: #336699; /* IE8 fallback */
            background: -webkit-gradient(linear, left top, right bottom, from(#336699), to(#003366));
            background: -webkit-linear-gradient(135deg, #336699, #003366);
            background: -moz-linear-gradient(135deg, #336699, #003366);
            background: -o-linear-gradient(135deg, #336699, #003366);
            background: linear-gradient(135deg, #336699, #003366);
        }

        /* 清除浮动 - IE8 兼容 */
        .clearfix:after {
            content: "";
            display: table;
            clear: both;
        }

        /* 隐藏元素 */
        .hidden {
            display: none;
        }

        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 32px;
            color: #aaccff;
            font-size: 16px;
        }

        /* IE8 响应式布局 - 简化版 */
        @media screen and (max-width: 768px) {
            .container {
                width: 100%;
                padding: 0 8px;
            }

            .sidebar {
                width: 100%;
                float: none;
                margin-right: 0;
                margin-bottom: 16px;
            }

            .main-content {
                margin-left: 0;
            }

            .software-icon-container {
                width: 50px;
                height: 50px;
                line-height: 50px;
                margin-right: 12px;
            }

            .software-icon {
                width: 30px;
                height: 30px;
            }

            .software-content {
                margin-left: 62px;
                margin-right: 0;
            }

            .software-actions {
                width: 100%;
                float: none;
                margin-top: 12px;
            }

            .download-btn, .video-demo-btn, .baidu-disk-btn, .purchase-btn {
                display: inline-block;
                width: 48%;
                margin-right: 2%;
                margin-bottom: 8px;
            }

            .pagination-info {
                float: none;
                text-align: center;
                margin-bottom: 12px;
            }

            .pagination-controls {
                float: none;
                text-align: center;
            }
        }
    </style>
</head>

<body>
    <!-- 苹果系统跳转脚本 -->
    <script>
        // 检测是否是苹果系统
        function isAppleDevice() {
            return /Mac|iPod|iPhone|iPad/.test(navigator.platform);
        }

        // 如果是苹果系统，跳转到指定网址
        if (isAppleDevice()) {
            // 替换为你想要跳转的苹果系统专用网址
            // window.location.href = "https://nbbrj.com/index1.html";
        }
    </script>

    <!-- 隐藏的音频播放器 -->
    <audio id="bg-music" loop="false" autoplay="true" style="display: none;">
        <source src="https://interstellar-software.s3.amazonaws.com/ambient-space-music.mp3" type="audio/mpeg">
    </audio>

    <!-- 进入弹窗公告 -->
    <div class="modal" id="announcementModal" <?php echo !$announcementEnabled ? 'style="display:none;"' : ''; ?>>
        <div class="modal-content" style="background: <?php echo $currentStyle['bg']; ?>; border-color: <?php echo $currentStyle['border']; ?>">
            <span class="modal-close" id="announcementClose">&times;</span>
            <h3 class="modal-title" style="color: <?php echo $currentStyle['title']; ?>"><?php echo htmlspecialchars($announcementTitle); ?></h3>
            <div class="modal-text"><?php echo $announcementContent; ?></div>
            <button class="modal-btn" id="announcementConfirm">我知道了</button>
        </div>
    </div>

    <!-- 通用弹窗容器 -->
    <div class="modal" id="universalModal">
        <div class="modal-content" id="universalContent">
            <!-- 内容将在这里动态替换 -->
        </div>
    </div>

    <div id="app">
        <header>
            <!-- 新增的关于我们按钮 -->
            <button class="about-btn" id="aboutBtn">
                <i class="fas fa-info-circle"></i> 关于我们
            </button>

            <h1><?php echo htmlspecialchars($siteTitle); ?></h1>
            <p class="subtitle"><?php echo htmlspecialchars($siteSubtitle); ?></p>
        </header>

        <!-- 二维码弹窗 -->
        <div class="modal" id="qrModal">
            <div class="modal-content">
                <span class="modal-close" id="qrClose">&times;</span>
                <h3 class="modal-title">扫描二维码关注我们</h3>
                <div style="width: 180px; height: 180px; margin: 0 auto 16px; background: white; padding: 10px; border-radius: 5px; position: relative;">
                    <!-- 使用配置中的二维码图片 -->
                    <img src="<?php echo isset($settings['basic']['qr_code_url']) ? htmlspecialchars($settings['basic']['qr_code_url']) : 'https://via.placeholder.com/200'; ?>" alt="二维码" style="width:100%;height:100%;">
                </div>
                <p class="modal-text">扫描上方二维码，获取更多软件资源和更新信息</p>
            </div>
        </div>

        <div class="container clearfix">
            <!-- 左侧分类导航 -->
            <aside class="sidebar">
                <h2>软件分类</h2>
                <ul class="category-list">
                    <li class="category-item active" data-category="all" id="category-all">
                        <a href="#" onclick="setCategory('all'); return false;">全部软件 <span class="category-badge" id="total-count">0个</span></a>
                    </li>
                    <!-- 分类列表将通过 JavaScript 动态加载 -->
                    <div id="categories-container"></div>
                </ul>
            </aside>

            <!-- 右侧内容区 -->
            <div class="main-content">
                <!-- 顶部搜索框 -->
                <div class="top-search <?php echo $searchMode === 'click' ? 'click-search' : 'instant-search'; ?>">
                    <input type="text" placeholder="搜索软件名称、..." id="searchInput">
                    <?php if ($searchMode !== 'instant'): ?>
                    <button onclick="search()" id="searchBtn"><i class="fa fa-search"></i> 搜索</button>
                    <?php endif; ?>
                </div>

                <main class="software-list">
                    <!-- 软件卡片将通过 JavaScript 动态加载 -->
                    <div id="software-container">
                        <div class="loading">正在加载软件列表...</div>
                    </div>

                    <!-- 搜索结果为空提示 -->
                    <div class="search-empty hidden" id="search-empty">
                        <h3>未找到匹配的软件</h3>
                        <p>请尝试使用不同的关键词或检查拼写</p>
                    </div>

                    <!-- 分页控件 -->
                    <div class="pagination-container hidden clearfix" id="pagination-container">
                        <div class="pagination-info" id="pagination-info">
                            共 0 条记录，第 1/1 页
                        </div>
                        <div class="pagination-controls">
                            <button onclick="changePage(1)" id="first-page" class="pagination-btn">
                                <i class="fas fa-angle-double-left"></i>
                            </button>
                            <button onclick="changePage(currentPage - 1)" id="prev-page" class="pagination-btn">
                                <i class="fas fa-angle-left"></i>
                            </button>
                            <button onclick="changePage(currentPage + 1)" id="next-page" class="pagination-btn">
                                <i class="fas fa-angle-right"></i>
                            </button>
                            <button onclick="changePage(totalPages)" id="last-page" class="pagination-btn">
                                <i class="fas fa-angle-double-right"></i>
                            </button>
                        </div>
                    </div>

                    <?php if (isset($frontendConfig['debug']) && $frontendConfig['debug']): ?>
                    <!-- 调试信息 -->
                    <div id="debug-info" class="hidden" style="margin-top: 20px; padding: 10px; background: rgba(0,0,0,0.8); color: #00ff00; font-family: monospace; font-size: 14px; border-radius: 5px; border: 2px solid #00ff00;">
                        <h3 style="color: #ff0000; margin-top: 0;">调试信息</h3>
                        <div id="debug-content"></div>
                    </div>
                    <?php endif; ?>
                </main>
            </div>
        </div>

        <footer>
            <p>© <?php echo date('Y'); ?> <?php echo htmlspecialchars($siteTitle); ?> | <?php echo htmlspecialchars($siteSubtitle); ?></p>
        </footer>
    </div>

    <!-- IE8 兼容的 JavaScript 代码 -->
    <script>
        // IE8 兼容的全局变量
        var currentPage = 1;
        var totalPages = 1;
        var pageSize = <?php echo $defaultPageSize; ?>;
        var totalSoftware = 0;
        var currentCategory = 'all';
        var currentSearch = '';
        var categories = [];
        var software = [];
        var searchTimeout = null;
        var debugEnabled = <?php echo $debugEnabled ? 'true' : 'false'; ?>;
        var searchMode = '<?php echo $searchMode; ?>';
        var searchDelay = <?php echo $searchDelay; ?>;

        // 支付配置
        var paymentConfig = {
            enabled: <?php
                $paymentConfig = get_payment_config();
                echo ($paymentConfig && $paymentConfig['enabled']) ? 'true' : 'false';
            ?>,
            available_payments: [
                <?php
                    if ($paymentConfig && $paymentConfig['enabled']) {
                        $payments = [];
                        if (isset($paymentConfig['wechat_pay']) && $paymentConfig['wechat_pay']['enabled']) {
                            $payments[] = '"wechat_pay"';
                        }
                        if (isset($paymentConfig['alipay']) && $paymentConfig['alipay']['enabled']) {
                            $payments[] = '"alipay"';
                        }
                        echo implode(',', $payments);
                    }
                ?>
            ],
            button_layout: '<?php
                echo isset($settings['payment']['button_layout']) ? $settings['payment']['button_layout'] : 'vertical';
            ?>',
            is_mobile: <?php echo is_mobile_device() ? 'true' : 'false'; ?>
        };

        // 站点配置
        var siteConfig = {
            frontendUrl: '<?php echo addslashes($settings['basic']['frontend_url'] ?? ''); ?>'
        };

        // 下载选项配置
        var downloadConfig = {
            <?php
                // 检查是否有下载配置
                if (isset($settings['download'])) {
                    $downloadSettings = $settings['download'];
                    for ($i = 1; $i <= 5; $i++) {
                        if (isset($downloadSettings["url_{$i}"])) {
                            $config = $downloadSettings["url_{$i}"];
                            echo "$i: {\n";
                            if (isset($config['name'])) {
                                echo "                name: '" . addslashes($config['name']) . "',\n";
                            }
                            if (isset($config['icon'])) {
                                echo "                icon: '" . addslashes($config['icon']) . "',\n";
                            }
                            if (isset($config['description'])) {
                                echo "                description: '" . addslashes($config['description']) . "',\n";
                            }
                            if (isset($config['useDownloadUrl'])) {
                                echo "                useDownloadUrl: " . ($config['useDownloadUrl'] ? 'true' : 'false') . "\n";
                            }
                            echo "            },\n";
                        }
                    }
                }
            ?>
        };

        // IE8 兼容的 AJAX 函数
        function createXHR() {
            if (typeof XMLHttpRequest !== 'undefined') {
                return new XMLHttpRequest();
            }
            try {
                return new ActiveXObject('Msxml2.XMLHTTP');
            } catch (e) {
                try {
                    return new ActiveXObject('Microsoft.XMLHTTP');
                } catch (e) {
                    return null;
                }
            }
        }

        function apiGet(url, callback, errorCallback) {
            var xhr = createXHR();
            if (!xhr) {
                if (errorCallback) errorCallback('不支持 AJAX');
                return;
            }

            xhr.open('GET', url, true);
            xhr.setRequestHeader('Content-Type', 'application/json');

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var data = JSON.parse(xhr.responseText);
                            if (callback) callback(data);
                        } catch (e) {
                            if (errorCallback) errorCallback('解析响应失败');
                        }
                    } else {
                        if (errorCallback) errorCallback('请求失败: ' + xhr.status);
                    }
                }
            };

            xhr.send();
        }

        function apiPost(url, data, callback, errorCallback) {
            var xhr = createXHR();
            if (!xhr) {
                if (errorCallback) errorCallback('不支持 AJAX');
                return;
            }

            xhr.open('POST', url, true);
            xhr.setRequestHeader('Content-Type', 'application/json');

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (callback) callback(response);
                        } catch (e) {
                            if (errorCallback) errorCallback('解析响应失败');
                        }
                    } else {
                        if (errorCallback) errorCallback('请求失败: ' + xhr.status);
                    }
                }
            };

            xhr.send(JSON.stringify(data));
        }

        // IE8 兼容的 DOM 操作函数
        function getElementById(id) {
            return document.getElementById(id);
        }

        function getElementsByClassName(className) {
            if (document.getElementsByClassName) {
                return document.getElementsByClassName(className);
            }
            // IE8 fallback
            var elements = document.getElementsByTagName('*');
            var result = [];
            for (var i = 0; i < elements.length; i++) {
                if (elements[i].className && elements[i].className.indexOf(className) !== -1) {
                    result.push(elements[i]);
                }
            }
            return result;
        }

        function addClass(element, className) {
            if (element.classList) {
                element.classList.add(className);
            } else {
                // IE8 fallback
                if (element.className.indexOf(className) === -1) {
                    element.className += ' ' + className;
                }
            }
        }

        function removeClass(element, className) {
            if (element.classList) {
                element.classList.remove(className);
            } else {
                // IE8 fallback
                element.className = element.className.replace(new RegExp('\\b' + className + '\\b', 'g'), '');
            }
        }

        function hasClass(element, className) {
            if (element.classList) {
                return element.classList.contains(className);
            } else {
                // IE8 fallback
                return element.className.indexOf(className) !== -1;
            }
        }

        // 显示/隐藏元素
        function showElement(element) {
            element.style.display = 'block';
        }

        function hideElement(element) {
            element.style.display = 'none';
        }

        // 弹窗管理
        function showModal(modalId) {
            var modal = getElementById(modalId);
            if (modal) {
                addClass(modal, 'active');
                showElement(modal);
            }
        }

        function hideModal(modalId) {
            var modal = getElementById(modalId);
            if (modal) {
                removeClass(modal, 'active');
                hideElement(modal);
            }
        }

        // 加载数据函数
        function loadData() {
            var url = '<?php echo $frontendConfig['api_path']; ?>index.php?flag=both';
            if (currentCategory !== 'all') {
                url += '&category=' + encodeURIComponent(currentCategory);
            }
            if (currentSearch) {
                url += '&search=' + encodeURIComponent(currentSearch);
            }
            url += '&page=' + currentPage + '&pageSize=' + pageSize;

            apiGet(url, function(data) {
                if (data) {
                    // 更新分类数据
                    if (data.categories) {
                        categories = data.categories;
                        renderCategories();
                    }

                    // 更新软件数据
                    if (data.software) {
                        software = data.software;
                        renderSoftware();
                    }

                    // 更新分页信息
                    if (data.pagination) {
                        currentPage = data.pagination.page;
                        totalPages = data.pagination.pages;
                        totalSoftware = data.pagination.total;
                        renderPagination();
                    }
                } else {
                    alert('加载数据失败: ' + (data.message || '未知错误'));
                }
            }, function(error) {
                alert('网络错误: ' + error);
            });
        }

        // 渲染分类列表
        function renderCategories() {
            var container = getElementById('categories-container');
            var html = '';

            for (var i = 0; i < categories.length; i++) {
                var category = categories[i];
                html += '<li class="category-item" data-category="' + category.name + '">';
                html += '<a href="#" onclick="setCategory(\'' + category.name + '\'); return false;">';
                html += category.name + ' <span class="category-badge">' + (category.software_count || 0) + '个</span>';
                html += '</a></li>';
            }

            container.innerHTML = html;

            // 更新总数
            var totalCount = getElementById('total-count');
            if (totalCount) {
                totalCount.innerHTML = totalSoftware + '个';
            }
        }

        // 渲染软件列表
        function renderSoftware() {
            var container = getElementById('software-container');
            var searchEmpty = getElementById('search-empty');

            if (software.length === 0) {
                container.innerHTML = '';
                removeClass(searchEmpty, 'hidden');
                return;
            }

            addClass(searchEmpty, 'hidden');

            var html = '';
            for (var i = 0; i < software.length; i++) {
                var item = software[i];
                html += renderSoftwareCard(item);
            }

            container.innerHTML = html;
        }

        // 渲染单个软件卡片
        function renderSoftwareCard(item) {
            var html = '<div class="software-card clearfix" data-category="' + getCategoryName(item.category) + '">';

            // 图标容器
            html += '<div class="software-icon-container">';
            html += '<img src="' + (item.icon || 'https://via.placeholder.com/50') + '" alt="' + item.name + '" class="software-icon">';
            html += '</div>';

            // 内容区域
            html += '<div class="software-content">';
            html += '<span class="software-badge badge-dev">' + getCategoryName(item.category) + '</span>';
            html += '<h3>' + item.name + '</h3>';
            html += '<p class="software-description">' + item.description + '</p>';
            html += '<div class="software-meta">';
            if (item.version) html += '<span>版本: ' + item.version + '</span>';
            if (item.size) html += '<span>大小: ' + item.size + '</span>';
            html += '<span>下载量: ' + item.display_downloads + '</span>';
            if (item.price) html += '<span>价格: ' + item.price + '</span>';
            html += '</div>';
            html += '</div>';

            // 操作按钮区域
            html += '<div class="software-actions">';

            // 价格为0或已购买显示下载选项，否则显示自助购买
            if (item.download_url && (item.price_value === 0 || item.has_paid)) {
                html += '<button class="download-btn" onclick="showDownloadOptions(' + item.id + ')">下载选项</button>';
            }
            if (item.download_url && item.price_value > 0 && !item.has_paid) {
                html += '<button class="purchase-btn" onclick="handlePurchase(' + item.id + ')">自助购买</button>';
            }
            if (item.video_url) {
                html += '<a href="' + item.video_url + '" class="video-demo-btn" target="_blank">视频演示</a>';
            }

            html += '</div>';
            html += '</div>';

            return html;
        }

        // 渲染分页
        function renderPagination() {
            var container = getElementById('pagination-container');
            var info = getElementById('pagination-info');

            if (totalPages <= 1) {
                addClass(container, 'hidden');
                return;
            }

            removeClass(container, 'hidden');

            // 更新分页信息
            if (info) {
                info.innerHTML = '共 ' + totalSoftware + ' 条记录，第 ' + currentPage + '/' + totalPages + ' 页';
            }

            // 更新按钮状态
            var firstBtn = getElementById('first-page');
            var prevBtn = getElementById('prev-page');
            var nextBtn = getElementById('next-page');
            var lastBtn = getElementById('last-page');

            if (currentPage === 1) {
                addClass(firstBtn, 'disabled');
                addClass(prevBtn, 'disabled');
            } else {
                removeClass(firstBtn, 'disabled');
                removeClass(prevBtn, 'disabled');
            }

            if (currentPage === totalPages) {
                addClass(nextBtn, 'disabled');
                addClass(lastBtn, 'disabled');
            } else {
                removeClass(nextBtn, 'disabled');
                removeClass(lastBtn, 'disabled');
            }
        }

        // 工具函数
        function getCategoryName(categoryId) {
            for (var i = 0; i < categories.length; i++) {
                if (categories[i].id == categoryId) {
                    return categories[i].name;
                }
            }
            return '未分类';
        }

        // 分类切换
        function setCategory(categoryName) {
            currentCategory = categoryName;
            currentPage = 1;

            // 更新分类选中状态
            var items = getElementsByClassName('category-item');
            for (var i = 0; i < items.length; i++) {
                removeClass(items[i], 'active');
            }

            var activeItem = getElementById('category-' + categoryName);
            if (!activeItem) {
                // 查找对应的分类项
                for (var i = 0; i < items.length; i++) {
                    if (items[i].getAttribute('data-category') === categoryName) {
                        activeItem = items[i];
                        break;
                    }
                }
            }

            if (activeItem) {
                addClass(activeItem, 'active');
            }

            loadData();
        }

        // 搜索功能
        function search() {
            var input = getElementById('searchInput');
            currentSearch = input.value;
            currentPage = 1;
            loadData();
        }

        // 即时搜索
        function onSearchInput() {
            if (searchMode !== 'instant') return;

            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            searchTimeout = setTimeout(function() {
                search();
            }, searchDelay);
        }

        // 分页功能
        function changePage(page) {
            if (page < 1 || page > totalPages || page === currentPage) {
                return;
            }
            currentPage = page;
            loadData();
        }

        // 下载选项弹窗
        function showDownloadOptions(softwareId) {
            // 查找软件信息
            var softwareItem = null;
            for (var i = 0; i < software.length; i++) {
                if (software[i].id == softwareId) {
                    softwareItem = software[i];
                    break;
                }
            }

            if (!softwareItem) {
                alert('软件信息不存在');
                return;
            }

            var modal = getElementById('universalModal');
            var content = getElementById('universalContent');

            var html = '<span class="modal-close" onclick="hideModal(\'universalModal\')">&times;</span>';
            html += '<h3 class="modal-title">下载选项 - ' + softwareItem.name + '</h3>';
            html += '<div class="modal-text">请选择下载方式：</div>';

            // 生成下载选项
            var downloadOptions = getDownloadOptions(softwareItem);
            for (var i = 0; i < downloadOptions.length; i++) {
                var option = downloadOptions[i];
                html += '<div style="margin-bottom: 12px; padding: 12px; background: rgba(0, 100, 255, 0.2); border-radius: 6px; border: 1px solid #334477; cursor: pointer;" onclick="downloadSoftware(' + softwareItem.id + ', ' + option.index + ')">';
                html += '<div style="color: #00ccff; font-weight: bold; margin-bottom: 4px;"><i class="' + option.icon + '"></i> ' + option.name + '</div>';
                html += '<div style="color: #aaccff; font-size: 12px;">' + option.description + '</div>';
                html += '</div>';
            }

            content.innerHTML = html;
            showModal('universalModal');
        }

        // 获取下载选项
        function getDownloadOptions(softwareItem) {
            var options = [];
            var defaultConfig = {
                1: { name: '本地下载', icon: 'fas fa-download', description: '从本地服务器下载最新版本', useDownloadUrl: true },
                2: { name: '备用下载', icon: 'fas fa-cloud-download-alt', description: '备用下载服务器，网络不佳时推荐', useDownloadUrl: false },
                3: { name: '百度网盘', icon: 'fab fa-baidu', description: '适合大文件下载，需登录百度账号', useDownloadUrl: false },
                4: { name: '下载方式4', icon: 'fas fa-link', description: '扩展下载方式', useDownloadUrl: false },
                5: { name: '下载方式5', icon: 'fas fa-external-link-alt', description: '扩展下载方式', useDownloadUrl: false }
            };

            for (var i = 1; i <= 5; i++) {
                var urlField = 'download_url_' + i;
                if (softwareItem[urlField]) {
                    var config = downloadConfig[i] || defaultConfig[i];
                    options.push({
                        index: i,
                        name: config.name,
                        icon: config.icon,
                        description: config.description,
                        url: softwareItem[urlField],
                        useDownloadUrl: config.useDownloadUrl
                    });
                }
            }

            return options;
        }

        // 下载软件
        function downloadSoftware(softwareId, urlIndex) {
            var url = '<?php echo $frontendConfig['api_path']; ?>download.php?id=' + softwareId;
            if (urlIndex) {
                url += '&url_index=' + urlIndex;
            }

            // 生成下载令牌
            var timestamp = Math.floor(Date.now() / 1000);
            var token = md5(softwareId + '_' + timestamp + '_download_secret');
            url += '&token=' + token + '&timestamp=' + timestamp;

            window.open(url, '_blank');
            hideModal('universalModal');
        }

        // 处理购买
        function handlePurchase(softwareId) {
            alert('购买功能需要完整的支付系统支持，当前为演示版本');
        }

        // 事件绑定
        function bindEvents() {
            // 搜索输入事件
            var searchInput = getElementById('searchInput');
            if (searchInput) {
                if (searchInput.addEventListener) {
                    searchInput.addEventListener('input', onSearchInput, false);
                    searchInput.addEventListener('keyup', function(e) {
                        if (e.keyCode === 13) { // Enter键
                            search();
                        }
                    }, false);
                } else if (searchInput.attachEvent) {
                    // IE8 fallback
                    searchInput.attachEvent('onpropertychange', onSearchInput);
                    searchInput.attachEvent('onkeyup', function(e) {
                        e = e || window.event;
                        if (e.keyCode === 13) {
                            search();
                        }
                    });
                }
            }

            // 关于我们按钮
            var aboutBtn = getElementById('aboutBtn');
            if (aboutBtn) {
                if (aboutBtn.addEventListener) {
                    aboutBtn.addEventListener('click', function() {
                        showModal('qrModal');
                    }, false);
                } else if (aboutBtn.attachEvent) {
                    aboutBtn.attachEvent('onclick', function() {
                        showModal('qrModal');
                    });
                }
            }

            // 弹窗关闭按钮
            var closeButtons = getElementsByClassName('modal-close');
            for (var i = 0; i < closeButtons.length; i++) {
                if (closeButtons[i].addEventListener) {
                    closeButtons[i].addEventListener('click', function() {
                        hideModal('qrModal');
                        hideModal('announcementModal');
                        hideModal('universalModal');
                    }, false);
                } else if (closeButtons[i].attachEvent) {
                    closeButtons[i].attachEvent('onclick', function() {
                        hideModal('qrModal');
                        hideModal('announcementModal');
                        hideModal('universalModal');
                    });
                }
            }

            // 公告确认按钮
            var announcementConfirm = getElementById('announcementConfirm');
            if (announcementConfirm) {
                if (announcementConfirm.addEventListener) {
                    announcementConfirm.addEventListener('click', function() {
                        hideModal('announcementModal');
                        // 保存到 localStorage
                        if (window.localStorage) {
                            localStorage.setItem('announcement_closed_' + <?php echo $announcementTimestamp; ?>, 'true');
                        }
                    }, false);
                } else if (announcementConfirm.attachEvent) {
                    announcementConfirm.attachEvent('onclick', function() {
                        hideModal('announcementModal');
                        // IE8 不支持 localStorage，使用 cookie
                        document.cookie = 'announcement_closed_<?php echo $announcementTimestamp; ?>=true; path=/';
                    });
                }
            }
        }

        // 初始化函数
        function init() {
            // 绑定事件
            bindEvents();

            // 检查是否显示公告
            var announcementEnabled = <?php echo $announcementEnabled ? 'true' : 'false'; ?>;
            if (announcementEnabled) {
                var announcementTimestamp = <?php echo $announcementTimestamp; ?>;
                var repeatShow = <?php echo $announcementRepeatShow ? 'true' : 'false'; ?>;
                var shouldShow = true;

                if (!repeatShow) {
                    // 检查是否已经关闭过
                    if (window.localStorage) {
                        shouldShow = !localStorage.getItem('announcement_closed_' + announcementTimestamp);
                    } else {
                        // IE8 fallback - 检查 cookie
                        shouldShow = document.cookie.indexOf('announcement_closed_' + announcementTimestamp + '=true') === -1;
                    }
                }

                if (shouldShow) {
                    showModal('announcementModal');
                }
            }

            // 检查 URL hash 是否有软件直链
            var hash = window.location.hash;
            if (hash) {
                var match = hash.match(/^#\/software\/(\d+)$/);
                if (match) {
                    var softwareId = match[1];
                    // 延迟处理，确保数据加载完成
                    setTimeout(function() {
                        showDownloadOptions(softwareId);
                    }, 1000);
                }
            }

            // 加载初始数据
            loadData();
        }

        // 页面加载完成后初始化
        if (document.addEventListener) {
            document.addEventListener('DOMContentLoaded', init, false);
        } else if (document.attachEvent) {
            // IE8 fallback
            document.attachEvent('onreadystatechange', function() {
                if (document.readyState === 'complete') {
                    init();
                }
            });
        } else {
            // 最后的 fallback
            window.onload = init;
        }

        // 调试功能
        if (debugEnabled) {
            function updateDebugInfo() {
                var debugInfo = getElementById('debug-info');
                var debugContent = getElementById('debug-content');
                if (debugInfo && debugContent) {
                    removeClass(debugInfo, 'hidden');
                    debugContent.innerHTML =
                        '<div><strong>分页配置:</strong> pageSize=' + pageSize + '</div>' +
                        '<div><strong>分页状态:</strong> page=' + currentPage + ', pages=' + totalPages + ', total=' + totalSoftware + '</div>' +
                        '<div><strong>当前分类:</strong> ' + currentCategory + '</div>' +
                        '<div><strong>搜索关键词:</strong> ' + currentSearch + '</div>' +
                        '<div><strong>软件数量:</strong> ' + software.length + '</div>' +
                        '<div><strong>调试模式:</strong> 开启</div>';
                }
            }

            // 每次数据更新后更新调试信息
            var originalLoadData = loadData;
            loadData = function() {
                originalLoadData();
                setTimeout(updateDebugInfo, 100);
            };
        }
    </script>

</body>
</html>
