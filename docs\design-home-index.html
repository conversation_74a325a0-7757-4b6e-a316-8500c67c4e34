<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <!-- 这是设计文档示例，实际项目中favicon应该根据basic.site_favicon设置动态显示 -->
    <link rel="icon" type="image/x-icon" href="./img/ji.ico">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>娜宝贝软件 - 高速下载</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: #0a0a1a;
            color: #e0e0ff;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 100, 255, 0.1) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(150, 0, 255, 0.1) 0%, transparent 20%);
            min-height: 100vh;
        }

        /* 头部样式 */
        header {
            background: linear-gradient(90deg, #001133, #003366);
            padding: 1rem;
            text-align: center;
            border-bottom: 2px solid #00aaff;
            box-shadow: 0 0 20px rgba(0, 170, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        header::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #00ffff, transparent);
            animation: scanline 4s linear infinite;
        }

        @keyframes scanline {
            0% {
                top: 0;
            }

            100% {
                top: 100%;
            }
        }

        h1 {
            font-size: 1.8rem;
            color: #00ffff;
            text-shadow: 0 0 10px #00aaff, 0 0 20px #0066ff;
            letter-spacing: 1px;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            color: #aaccff;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
        }

        /* 主要内容区 */
        .container {
            display: flex;
            flex-direction: column;
            max-width: 1200px;
            margin: 1rem auto;
            padding: 0 1rem;
        }

        /* 左侧分类导航 */
        .sidebar {
            width: 100%;
            background: rgba(10, 20, 40, 0.7);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            border: 1px solid #334466;
            box-shadow: 0 0 15px rgba(0, 100, 255, 0.2);
            backdrop-filter: blur(5px);
        }

        .sidebar h2 {
            color: #00ccff;
            font-size: 1.2rem;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #334477;
            text-shadow: 0 0 5px #00aaff;
        }

        .category-list {
            list-style: none;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 0.5rem;
        }

        .category-item {
            margin-bottom: 0;
            position: relative;
            transition: all 0.3s ease;
        }

        .category-item::before {
            content: "▶";
            color: #00aaff;
            position: absolute;
            left: -1rem;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .category-item:hover::before {
            opacity: 1;
            left: -0.5rem;
        }

        .category-item a {
            color: #aaccff;
            text-decoration: none;
            display: block;
            padding: 0.5rem;
            border-radius: 4px;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
        }

        .category-item a:hover {
            background: rgba(0, 100, 255, 0.2);
            color: #00ffff;
            transform: translateX(5px);
        }

        .category-item.active a {
            background: rgba(0, 100, 255, 0.3);
            color: #00ffff;
            border-left: 3px solid #00aaff;
        }

        .category-badge {
            background: rgba(0, 170, 255, 0.3);
            color: #00ffff;
            padding: 0.1rem 0.3rem;
            border-radius: 999px;
            font-size: 0.7rem;
        }

        /* 新增的二级分类样式 */
        .has-submenu {
            position: relative;
        }
        
        .submenu {
            display: none;
            list-style: none;
            padding-left: 1rem;
            margin-top: 0.5rem;
            border-left: 1px solid #334477;
        }
        
        .has-submenu.active .submenu {
            display: block;
        }
        
        .subcategory-item a {
            padding-left: 1.5rem;
            position: relative;
        }
        
        .subcategory-item a::before {
            content: "•";
            color: #00aaff;
            position: absolute;
            left: 0.5rem;
        }
        
        .has-submenu > a > i {
            font-size: 0.7rem;
            transition: transform 0.3s ease;
        }
        
        .has-submenu.active > a > i {
            transform: rotate(90deg);
        }

        /* 右侧内容区 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* 顶部搜索框 */
        .top-search {
            margin-bottom: 1rem;
            position: relative;
            width: 100%;
            height: 50px;
            background: rgba(10, 20, 40, 0.7);
            border-radius: 8px;
            border: 1px solid #334466;
            box-shadow: 0 0 15px rgba(0, 100, 255, 0.2);
            padding: 0.5rem;
            overflow: hidden;
        }

        .top-search::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 170, 255, 0.2), transparent);
            animation: search-glow 3s infinite;
        }

        @keyframes search-glow {
            0% {
                left: -100%;
            }

            100% {
                left: 100%;
            }
        }

        .top-search input {
            width: 100%;
            height: 100%;
            padding: 0 1rem;
            background: transparent;
            border: none;
            color: #e0e0ff;
            outline: none;
            font-size: 1rem;
        }

        .top-search button {
            position: absolute;
            right: 0.5rem;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, #00aaff, #0066ff);
            color: #ffffff;
            border: none;
            border-radius: 4px;
            padding: 0.4rem 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 0 5px rgba(0, 170, 255, 0.3);
            font-size: 0.9rem;
        }

        .top-search button:hover {
            background: linear-gradient(135deg, #00ccff, #0088ff);
            box-shadow: 0 0 10px rgba(0, 170, 255, 0.5);
        }

        /* 软件列表 */
        .software-list {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .software-card {
            background: rgba(20, 30, 60, 0.6);
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #445588;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeIn 0.5s forwards;
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .software-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 100, 255, 0.2);
            border-color: #00aaff;
        }

        .software-card::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 170, 255, 0.1) 0%, transparent 50%);
            z-index: -1;
        }

        /* 分类徽章 */
        .software-badge {
            display: inline-block;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            margin-bottom: 0.5rem;
        }

        .badge-dev {
            background: rgba(0, 170, 255, 0.2);
            color: #00aaff;
        }

        .badge-design {
            background: rgba(170, 0, 255, 0.2);
            color: #aa00ff;
        }

        .badge-security {
            background: rgba(0, 255, 170, 0.2);
            color: #00ffaa;
        }

        .badge-browser {
            background: rgba(255, 170, 0, 0.2);
            color: #ffaa00;
        }

        .badge-system {
            background: rgba(255, 0, 170, 0.2);
            color: #ff00aa;
        }

        /* 图标区域 */
        .software-icon-container {
            width: 60px;
            height: 60px;
            margin-right: 1rem;
            margin-bottom: 0.5rem;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 100, 255, 0.1);
            border-radius: 8px;
            border: 1px solid #334477;
            position: relative;
            overflow: hidden;
        }

        .software-icon {
            width: 40px;
            height: 40px;
            object-fit: contain;
        }

        /* 内容区域 */
        .software-content {
            flex: 1;
        }

        .software-content h3 {
            color: #00ccff;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .software-description {
            color: #aaccff;
            font-size: 0.85rem;
            line-height: 1.4;
            margin-bottom: 0.8rem;
        }

        .software-meta {
            display: flex;
            flex-wrap: wrap;
            color: #7788aa;
            font-size: 0.8rem;
            gap: 0.8rem;
            margin-bottom: 0.8rem;
        }

        /* 下载按钮区域 */
        .software-actions {
            width: 100%;
            flex-shrink: 0;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 0.5rem;
        }

        .download-btn, .video-demo-btn, .baidu-disk-btn, .purchase-btn {
            display: inline-block;
            color: #ffffff;
            padding: 0.6rem 0.5rem;
            border-radius: 4px;
            text-decoration: none;
            font-weight: bold;
            text-align: center;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            font-size: 0.85rem;
        }

        .download-btn {
            background: linear-gradient(135deg, #00aaff, #0066ff);
        }

        .download-btn:hover {
            background: linear-gradient(135deg, #00ccff, #0088ff);
            box-shadow: 0 0 10px rgba(0, 170, 255, 0.5);
        }

        .download-btn::after {
            content: "↓";
            position: absolute;
            right: 0.5rem;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .download-btn:hover::after {
            opacity: 1;
            right: 0.3rem;
        }

        /* 视频演示按钮 */
        .video-demo-btn {
            background: linear-gradient(135deg, #00aaff, #0066ff);
        }

        .video-demo-btn:hover {
            background: linear-gradient(135deg, #00ccff, #0088ff);
            box-shadow: 0 0 10px rgba(0, 170, 255, 0.5);
        }

        .video-demo-btn::after {
            content: "▶";
            position: absolute;
            right: 0.5rem;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .video-demo-btn:hover::after {
            opacity: 1;
            right: 0.3rem;
        }

        /* 百度网盘下载按钮 */
        .baidu-disk-btn {
            background: linear-gradient(135deg, #00aa77, #006644);
        }

        .baidu-disk-btn:hover {
            background: linear-gradient(135deg, #00cc99, #008866);
            box-shadow: 0 0 10px rgba(0, 170, 120, 0.5);
        }

        .baidu-disk-btn::after {
            content: "云";
            position: absolute;
            right: 0.5rem;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .baidu-disk-btn:hover::after {
            opacity: 1;
            right: 0.3rem;
        }

        /* 自助购买按钮 */
        .purchase-btn {
            background: linear-gradient(135deg, #ff5500, #aa3300);
        }

        .purchase-btn:hover {
            background: linear-gradient(135deg, #ff7722, #cc5500);
            box-shadow: 0 0 10px rgba(255, 100, 0, 0.5);
        }

        .purchase-btn::after {
            content: "购";
            position: absolute;
            right: 0.5rem;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .purchase-btn:hover::after {
            opacity: 1;
            right: 0.3rem;
        }

        /* 页脚 */
        footer {
            text-align: center;
            padding: 1.5rem;
            color: #7788aa;
            font-size: 0.8rem;
            border-top: 1px solid #223344;
            margin-top: 1.5rem;
        }

        /* 修改后的关于我们按钮样式 */
        .about-btn {
            position: absolute;
            right: 0.5rem;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 170, 255, 0.2);
            color: #00ccff;
            border: 1px solid #00aaff;
            border-radius: 4px;
            padding: 0.4rem 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
            box-shadow: 0 0 10px rgba(0, 170, 255, 0.3);
            font-size: 0.8rem;
        }

        .about-btn:hover {
            background: rgba(0, 170, 255, 0.4);
            box-shadow: 0 0 15px rgba(0, 170, 255, 0.5);
        }

        /* 二维码弹窗样式 */
        .qr-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 30, 0.8);
            backdrop-filter: blur(5px);
            z-index: 100;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .qr-modal.active {
            display: flex;
            opacity: 1;
        }

        .qr-content {
            background: linear-gradient(135deg, #001133, #003366);
            padding: 1.5rem;
            border-radius: 10px;
            border: 1px solid #00aaff;
            box-shadow: 0 0 30px rgba(0, 170, 255, 0.5);
            text-align: center;
            position: relative;
            max-width: 90%;
            width: 300px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 30px rgba(0, 170, 255, 0.5);
            }

            50% {
                box-shadow: 0 0 50px rgba(0, 170, 255, 0.8);
            }

            100% {
                box-shadow: 0 0 30px rgba(0, 170, 255, 0.5);
            }
        }

        .qr-content h3 {
            color: #00ffff;
            margin-bottom: 1rem;
            text-shadow: 0 0 10px #00aaff;
            font-size: 1.2rem;
        }

        .qr-code {
            width: 180px;
            height: 180px;
            margin: 0 auto 1rem;
            background: white;
            padding: 10px;
            border-radius: 5px;
            position: relative;
        }

        .qr-code::before {
            content: "";
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border: 2px solid #00ffff;
            border-radius: 8px;
            animation: borderGlow 2s infinite;
            z-index: -1;
        }

        @keyframes borderGlow {
            0% {
                box-shadow: 0 0 10px #00aaff;
            }

            50% {
                box-shadow: 0 0 20px #00ffff;
            }

            100% {
                box-shadow: 0 0 10px #00aaff;
            }
        }

        .qr-close {
            position: absolute;
            top: 10px;
            right: 10px;
            color: #00ffff;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .qr-close:hover {
            transform: rotate(90deg);
            color: #ff0000;
        }

        .qr-desc {
            color: #aaccff;
            font-size: 0.8rem;
            margin-top: 1rem;
        }

        /* 进入弹窗公告样式 */
        .announcement-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 30, 0.8);
            backdrop-filter: blur(5px);
            z-index: 200;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .announcement-modal.active {
            display: flex;
            opacity: 1;
        }

        .announcement-content {
            background: linear-gradient(135deg, #001133, #003366);
            padding: 1.5rem;
            border-radius: 10px;
            border: 1px solid #00aaff;
            box-shadow: 0 0 30px rgba(0, 170, 255, 0.5);
            text-align: center;
            position: relative;
            max-width: 90%;
            width: 500px;
            animation: pulse 2s infinite;
        }

        .announcement-close {
            position: absolute;
            top: 10px;
            right: 10px;
            color: #00ffff;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .announcement-close:hover {
            transform: rotate(90deg);
            color: #ff0000;
        }

        .announcement-title {
            color: #00ffff;
            font-size: 1.3rem;
            margin-bottom: 1rem;
            text-shadow: 0 0 10px #00aaff;
        }

        .announcement-text {
            color: #aaccff;
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: 1.2rem;
        }

        .announcement-btn {
            display: inline-block;
            background: linear-gradient(135deg, #00aaff, #0066ff);
            color: #ffffff;
            padding: 0.6rem 1.2rem;
            border-radius: 4px;
            text-decoration: none;
            font-weight: bold;
            text-align: center;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .announcement-btn:hover {
            background: linear-gradient(135deg, #00ccff, #0088ff);
            box-shadow: 0 0 10px rgba(0, 170, 255, 0.5);
        }

        /* 搜索结果为空提示 */
        .search-empty {
            text-align: center;
            padding: 2rem 0;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeIn 0.5s forwards;
        }

        .search-empty h3 {
            color: #00ccff;
            font-size: 1.2rem;
            margin-bottom: 0.8rem;
        }

        .search-empty p {
            color: #aaccff;
            font-size: 0.9rem;
        }

        /* 新增下载弹窗样式 */
        .download-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 30, 0.8);
            backdrop-filter: blur(5px);
            z-index: 150;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .download-modal.active {
            display: flex;
            opacity: 1;
        }
        
        .download-content {
            background: linear-gradient(135deg, #001133, #003366);
            padding: 1.5rem;
            border-radius: 10px;
            border: 1px solid #00aaff;
            box-shadow: 0 0 30px rgba(0, 170, 255, 0.5);
            text-align: center;
            position: relative;
            max-width: 90%;
            width: 500px;
            animation: pulse 2s infinite;
        }
        
        .download-title {
            color: #00ffff;
            font-size: 1.3rem;
            margin-bottom: 1rem;
            text-shadow: 0 0 10px #00aaff;
        }
        
        .download-note {
            color: #aaccff;
            font-size: 0.9rem;
            margin: 1rem 0;
            padding: 0.8rem;
            background: rgba(0, 50, 100, 0.3);
            border-radius: 6px;
            border-left: 3px solid #00aaff;
            text-align: left;
        }
        
        .download-options {
            display: flex;
            flex-direction: column;
            gap: 0.8rem;
            margin-bottom: 1.5rem;
        }
        
        .download-option {
            display: flex;
            align-items: center;
            padding: 0.8rem;
            background: rgba(0, 100, 255, 0.2);
            border-radius: 6px;
            border: 1px solid #334477;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .download-option:hover {
            background: rgba(0, 170, 255, 0.3);
            border-color: #00aaff;
        }
        
        .download-option-icon {
            width: 30px;
            height: 30px;
            margin-right: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 170, 255, 0.2);
            border-radius: 50%;
            color: #00aaff;
        }
        
        .download-option-text {
            flex: 1;
            text-align: left;
        }
        
        .download-option-name {
            color: #00ccff;
            font-weight: bold;
            margin-bottom: 0.2rem;
        }
        
        .download-option-desc {
            color: #aaccff;
            font-size: 0.8rem;
        }
        
        .download-option-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .download-copy-btn, .download-now-btn {
            background: rgba(0, 170, 255, 0.3);
            color: #00ffff;
            border: none;
            border-radius: 4px;
            padding: 0.3rem 0.6rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }
        
        .download-now-btn {
            background: rgba(0, 200, 100, 0.3);
        }
        
        .download-now-btn:hover {
            background: rgba(0, 200, 100, 0.5);
        }
        
        .download-copy-btn:hover {
            background: rgba(0, 170, 255, 0.5);
        }

        /* 平板和桌面布局 */
        @media (min-width: 768px) {
            header {
                padding: 1.5rem;
            }

            h1 {
                font-size: 2rem;
            }

            .container {
                flex-direction: row;
                margin: 2rem auto;
            }

            .sidebar {
                width: 250px;
                margin-right: 1.5rem;
                margin-bottom: 0;
            }

            .category-list {
                display: block;
                grid-template-columns: none;
            }

            /* 二级分类菜单调整 */
            .submenu {
                padding-left: 1.5rem;
            }
            
            .subcategory-item a {
                padding-left: 2rem;
            }

            .software-card {
                flex-direction: row;
                padding: 1.5rem;
                align-items: center;
            }

            .software-icon-container {
                width: 80px;
                height: 80px;
                margin-bottom: 0;
            }

            .software-icon {
                width: 50px;
                height: 50px;
            }

            .software-content h3 {
                font-size: 1.2rem;
            }

            .software-description {
                font-size: 0.9rem;
            }

            .software-actions {
                width: 150px;
                display: flex;
                flex-direction: column;
                grid-template-columns: none;
            }

            .download-btn, .video-demo-btn, .baidu-disk-btn, .purchase-btn {
                padding: 0.6rem 1rem;
                font-size: 0.85rem;
            }
        }

        /* 大屏幕布局 */
        @media (min-width: 992px) {
            h1 {
                font-size: 2.5rem;
            }

            .subtitle {
                font-size: 1rem;
            }

            .software-content h3 {
                font-size: 1.3rem;
            }

            .software-description {
                font-size: 0.95rem;
            }
        }
    </style>
</head>

<body>
    <!-- 苹果系统跳转脚本 -->
    <script>
        // 检测是否是苹果系统
        function isAppleDevice() {
            return /Mac|iPod|iPhone|iPad/.test(navigator.platform);
        }

        // 如果是苹果系统，跳转到指定网址
        if (isAppleDevice()) {
            // 替换为你想要跳转的苹果系统专用网址
            window.location.href = "https://nbbrj.com/index1.html";
        }
    </script>

    <!-- 隐藏的音频播放器 -->
    <audio id="bg-music" loop="false" autoplay="true" style="display: none;">
        <source src="https://interstellar-software.s3.amazonaws.com/ambient-space-music.mp3" type="audio/mpeg">
    </audio>

    <!-- 进入弹窗公告 -->
    <div class="announcement-modal" id="announcementModal">
        <div class="announcement-content">
            <span class="announcement-close" id="announcementClose">&times;</span>
            <h3 class="announcement-title">重要公告</h3>
            <p class="announcement-text">欢迎来到娜宝贝软件官网！我们秉着良好的态度和服务，让我的客户优先享用最好用的下载服务，极速的下载体验，让您感受不一样的下载方式！</p>
            <button class="announcement-btn" id="announcementConfirm">我知道了</button>
        </div>
    </div>

    <header>
        <!-- 新增的关于我们按钮 -->
        <button class="about-btn" id="aboutBtn">
            <i class="fas fa-info-circle"></i> 关于我们
        </button>

        <h1>娜宝贝软件</h1>
        <a href=http://pe.521559.com/d/%E5%A4%A9%E7%BF%BC%E5%AE%B6%E5%BA%AD%E4%BA%91%E7%9B%98/windows/%E5%AE%89%E5%A8%9C%E7%BD%91%E7%BB%9C%E7%B3%BB%E7%BB%9F%E9%87%8D%E8%A3%85.exe ">nbbrj.com</a>
        <h1>nbbrj.com</h1>

        <p class="subtitle">探索未来科技的软件集合</p>
    </header>

    <!-- 二维码弹窗 -->
    <div class="qr-modal" id="qrModal">
        <div class="qr-content">
            <span class="qr-close" id="qrClose">&times;</span>
            <h3>扫描二维码关注我们</h3>
            <div class="qr-code">
                <!-- 这里替换为你的二维码图片 -->
                <img src="https://via.placeholder.com/200" alt="二维码" style="width:100%;height:100%;">
            </div>
            <p class="qr-desc">扫描上方二维码，获取更多软件资源和更新信息</p>
        </div>
    </div>

    <!-- 下载选项弹窗 -->
    <div class="download-modal" id="downloadModal">
        <div class="download-content">
            <span class="qr-close" id="downloadClose">&times;</span>
            <h3 class="download-title" id="downloadTitle">选择下载方式</h3>
            
            <div class="download-note">
                <p><strong>下载说明：</strong></p>
                <p>1. 请根据您的网络环境选择合适的下载方式</p>
                <p>2. 下载过程中请勿关闭页面</p>
                <p>3. 如遇下载失败，请尝试其他下载方式</p>
                <p>4. 下载完成后请检查文件完整性</p>
            </div>
            
            <div class="download-options" id="downloadOptions">
                <!-- 下载选项将在这里动态添加 -->
            </div>
            
            <button class="announcement-btn" id="downloadCancel">取消</button>
        </div>
    </div>

    <div class="container">
        <!-- 左侧分类导航 -->
        <aside class="sidebar">
            <h2>软件分类</h2>
            <ul class="category-list">
                <li class="category-item active" data-category="all">
                    <a href="#">全部软件 <span class="category-badge">0个</span></a>
                </li>
                
                <!-- 一级分类 - 远程软件 -->
                <li class="category-item has-submenu" data-category="远程软件">
                    <a href="#">远程软件 <i class="fas fa-chevron-right"></i> <span class="category-badge">0个</span></a>
                    <ul class="submenu">
                        <li class="subcategory-item" data-category="国内远程">
                            <a href="#">国内远程 <span class="category-badge">0个</span></a>
                        </li>
                        <li class="subcategory-item" data-category="国外远程">
                            <a href="#">国外远程 <span class="category-badge">0个</span></a>
                        </li>
                        <li class="subcategory-item" data-category="企业版">
                            <a href="#">企业版 <span class="category-badge">0个</span></a>
                        </li>
                    </ul>
                </li>
                
                <!-- 一级分类 - Autodesk软件 -->
                <li class="category-item has-submenu" data-category="Autodesk软件">
                    <a href="#">Autodesk软件 <i class="fas fa-chevron-right"></i> <span class="category-badge">0个</span></a>
                    <ul class="submenu">
                        <li class="subcategory-item" data-category="AutoCAD">
                            <a href="#">AutoCAD <span class="category-badge">0个</span></a>
                        </li>
                        <li class="subcategory-item" data-category="3ds Max">
                            <a href="#">3ds Max <span class="category-badge">0个</span></a>
                        </li>
                        <li class="subcategory-item" data-category="Maya">
                            <a href="#">Maya <span class="category-badge">0个</span></a>
                        </li>
                        <li class="subcategory-item" data-category="Revit">
                            <a href="#">Revit <span class="category-badge">0个</span></a>
                        </li>
                    </ul>
                </li>
                
                <!-- 一级分类 - 三维设计 -->
                <li class="category-item has-submenu" data-category="三维设计">
                    <a href="#">三维设计 <i class="fas fa-chevron-right"></i> <span class="category-badge">0个</span></a>
                    <ul class="submenu">
                        <li class="subcategory-item" data-category="SketchUp">
                            <a href="#">SketchUp <span class="category-badge">0个</span></a>
                        </li>
                        <li class="subcategory-item" data-category="Blender">
                            <a href="#">Blender <span class="category-badge">0个</span></a>
                        </li>
                        <li class="subcategory-item" data-category="Cinema 4D">
                            <a href="#">Cinema 4D <span class="category-badge">0个</span></a>
                        </li>
                    </ul>
                </li>
                
                <!-- 其他一级分类保持不变 -->
                <li class="category-item" data-category="office">
                    <a href="#">办公软件 <span class="category-badge">0个</span></a>
                </li>
                <li class="category-item" data-category="media">
                    <a href="#">媒体播放 <span class="category-badge">0个</span></a>
                </li>
                <li class="category-item" data-category="security">
                    <a href="#">安全防护 <span class="category-badge">0个</span></a>
                </li>
                <li class="category-item" data-category="network">
                    <a href="#">网络工具 <span class="category-badge">0个</span></a>
                </li>
                <li class="category-item" data-category="game">
                    <a href="#">游戏娱乐 <span class="category-badge">0个</span></a>
                </li>
                <li class="category-item" data-category="education">
                    <a href="#">教育学习 <span class="category-badge">0个</span></a>
                </li>
            </ul>
        </aside>

        <!-- 右侧软件列表 - 单行卡片布局 -->
        <div class="main-content">
            <!-- 顶部搜索框 -->
            <div class="top-search">
                <input type="text" placeholder="搜索软件名称、..." id="search-input">
                <button id="search-button"><i class="fa fa-search"></i> 搜索</button>
            </div>

            <main class="software-list">
                <!-- 软件卡片 1 - 国内远程软件 -->
                <div class="software-card" data-category="国内远程">
                    <div class="software-icon-container">
                        <img src="https://nbbrj.com/png/todesk.png" alt="Todesk远程软件" class="software-icon">
                    </div>
                    <div class="software-content">
                        <span class="software-badge badge-dev">国内远程</span>
                        <h3>Todesk远程软件</h3>
                        <p class="software-description">ToDesk 是一款多平台远程协作软件，支持主流操作系统Windows、Linux、Mac、Android、iOS跨平台协同操作。</p>
                        <div class="software-meta">
                            <span>版本: v4.7.7.0</span>
                            <span>大小: 25.27 MB</span>
                            <span>下载量: 12.8万</span>
                        </div>
                    </div>
                    <div class="software-actions">
                        <button class="download-btn" 
                                data-name="Todesk远程软件"
                                data-options='[
                                    {
                                        "name": "官方下载",
                                        "url": "https://dl.todesk.com/windows/ToDesk_Lite.exe",
                                        "icon": "fas fa-download",
                                        "desc": "从官方服务器下载最新版本"
                                    },
                                    {
                                        "name": "备用下载",
                                        "url": "https://mirror.todesk.com/windows/ToDesk_Lite.exe",
                                        "icon": "fas fa-cloud-download-alt",
                                        "desc": "当官方下载不可用时使用"
                                    },
                                    {
                                        "name": "百度网盘",
                                        "url": "https://pan.baidu.com/s/1example",
                                        "icon": "fab fa-baidu",
                                        "desc": "适合大文件下载，需登录百度账号"
                                    }
                                ]'>
                            立即下载
                        </button>
                    </div>
                </div>

                <!-- 软件卡片 2 - 国外远程 -->
                <div class="software-card" data-category="国外远程">
                    <div class="software-icon-container">
                        <img src="https://nbbrj.com/png/DeskIn.png" alt="HoloDesign Pro" class="software-icon">
                    </div>
                    <div class="software-content">
                        <span class="software-badge badge-design">国外远程</span>
                        <h3>DeskIn远程【国外专用】</h3>
                        <p class="software-description">全息3D设计工具，支持神经接口输入和多人协同创作，设计师的未来之选。可在虚拟空间中直接建模，实时渲染效果惊艳。</p>
                        <div class="software-meta">
                            <span>版本: v3.3.0</span>
                            <span>大小: 54.54 MB</span>
                            <span>下载量: 0.7万</span>
                            <span>价格: ¥免费版</span>
                        </div>
                    </div>
                    <div class="software-actions">
                        <button class="download-btn" 
                                data-name="DeskIn远程【国外专用】"
                                data-options='[
                                    {
                                        "name": "官方下载",
                                        "url": "https://dl.deskin.io/windows/DeskIn_Setup_v3.3.0.0_x64.exe",
                                        "icon": "fas fa-download",
                                        "desc": "从官方服务器下载最新版本"
                                    },
                                    {
                                        "name": "备用下载",
                                        "url": "https://mirror.deskin.io/windows/DeskIn_Setup_v3.3.0.0_x64.exe",
                                        "icon": "fas fa-cloud-download-alt",
                                        "desc": "当官方下载不可用时使用"
                                    },
                                    {
                                        "name": "百度网盘",
                                        "url": "https://pan.baidu.com/s/1example2",
                                        "icon": "fab fa-baidu",
                                        "desc": "适合大文件下载，需登录百度账号"
                                    }
                                ]'>
                            立即下载
                        </button>
                    </div>
                </div>

                <!-- 软件卡片  - AutoCAD -->
                <div class="software-card" data-category="AutoCAD">
                    <div class="software-icon-container">
                        <img src="https://nbbrj.com/png/2023.png" alt="Autodesk软件" class="software-icon">
                    </div>
                    <div class="software-content">
                        <span class="software-badge badge-dev">Autodesk绘图软件</span>
                        <h3>AutoCAD下载</h3>
                        <p class="software-description">点击立即下载，下载的工具运行，把客服发您的密码输入进去就可以极速下载，安装教程都在程序里面。全套2007-2026版本</p>
                        <div class="software-meta">
                            <span>版本: v4.7.7.0</span>
                            <span>大小: 25.27 MB</span>
                            <span>下载量: 12.8万</span>
                            <span>价格: ¥付费版，可自助购买，自动发密码</span>
                        </div>
                    </div>
                    <div class="software-actions">
                        <a href="#" class="video-demo-btn">视频演示</a>
                        <button class="download-btn" 
                                data-name="AutoCAD下载"
                                data-options='[
                                    {
                                        "name": "官方下载",
                                        "url": "https://nbbrj.com/Setup/CAD_Setup.exe",
                                        "icon": "fas fa-download",
                                        "desc": "下载AutoCAD安装程序"
                                    },
                                    {
                                        "name": "备用下载",
                                        "url": "https://mirror.nbbrj.com/Setup/CAD_Setup.exe",
                                        "icon": "fas fa-cloud-download-alt",
                                        "desc": "当官方下载不可用时使用"
                                    },
                                    {
                                        "name": "百度网盘",
                                        "url": "https://pan.baidu.com/s/1-TfOfcSCaKzzjt7mkTz5Cw",
                                        "icon": "fab fa-baidu",
                                        "desc": "适合大文件下载，需登录百度账号"
                                    }
                                ]'>
                            立即下载
                        </button>
                        <button class="purchase-btn" onclick="window.location.href='https://item.taobao.com/item.htm?id=744987530810'">自助购买</button>
                    </div>
                </div>

                <!-- 软件卡片  - AutoCAD插件 -->
                <div class="software-card" data-category="AutoCAD">
                    <div class="software-icon-container">
                        <img src="https://nbbrj.com/png/tz.png" alt="Autodesk软件" class="software-icon">
                    </div>
                    <div class="software-content">
                        <span class="software-badge badge-dev">CAD天正插件</span>
                        <h3>CAD天正插件</h3>
                        <p class="software-description">CAD天正插件，包含，T20V8 T30V1支持CAD2010-2026.包含【建筑/暖通/电气/给排水/结构】</p>
                        <div class="software-meta">
                            <span>版本: v8 T30v1</span>
                            <span>大小: 2.6 MB</span>
                            <span>下载量: 0.8万</span>
                            <span>价格: ¥付费版，可自助购买，自动发密码</span>
                        </div>
                    </div>
                    <div class="software-actions">
                        <a href="#" class="video-demo-btn">视频演示</a>
                        <button class="download-btn" 
                                data-name="CAD天正插件"
                                data-options='[
                                    {
                                        "name": "官方下载",
                                        "url": "https://nbbrj.com/Setup/TZ_Setup.exe",
                                        "icon": "fas fa-download",
                                        "desc": "下载天正插件安装程序"
                                    },
                                    {
                                        "name": "备用下载",
                                        "url": "https://mirror.nbbrj.com/Setup/TZ_Setup.exe",
                                        "icon": "fas fa-cloud-download-alt",
                                        "desc": "当官方下载不可用时使用"
                                    },
                                    {
                                        "name": "百度网盘",
                                        "url": "https://pan.baidu.com/s/1B1VYO6XwPfkfHoFyrBqsuQ",
                                        "icon": "fab fa-baidu",
                                        "desc": "适合大文件下载，需登录百度账号"
                                    }
                                ]'>
                            立即下载
                        </button>
                        <button class="purchase-btn" onclick="window.location.href='https://item.taobao.com/item.htm?ft=t&id=745088871503&spm=a21dvs.23580594.0.0.52de2c1bcfk5qG'">自助购买</button>
                    </div>
                </div>

                <!-- 软件卡片  - SketchUp -->
                <div class="software-card" data-category="SketchUp">
                    <div class="software-icon-container">
                        <img src="https://nbbrj.com/png/Su.png" alt="三维设计" class="software-icon">
                    </div>
                    <div class="software-content">
                        <span class="software-badge badge-dev">三维设计</span>
                        <h3>SketchupPro_草图大师</h3>
                        <p class="software-description">su版本包含2016-2025版本/并且还包含了VR插件4-5-6-7版本的VR插件，支持Su2016-2025</p>
                        <div class="software-meta">
                            <span>版本: v8 T30v1</span>
                            <span>大小: 2.6 MB</span>
                            <span>下载量: 0.8万</span>
                            <span>价格: ¥付费版，可自助购买，自动发密码</span>
                        </div>
                    </div>
                    <div class="software-actions">
                        <a href="#" class="video-demo-btn">视频演示</a>
                        <button class="download-btn" 
                                data-name="SketchupPro_草图大师"
                                data-options='[
                                    {
                                        "name": "官方下载",
                                        "url": "https://nbbrj.com/Setup/SketchUp_Setup.exe",
                                        "icon": "fas fa-download",
                                        "desc": "下载Sketchup安装程序"
                                    },
                                    {
                                        "name": "备用下载",
                                        "url": "https://mirror.nbbrj.com/Setup/SketchUp_Setup.exe",
                                        "icon": "fas fa-cloud-download-alt",
                                        "desc": "当官方下载不可用时使用"
                                    },
                                    {
                                        "name": "百度网盘",
                                        "url": "https://pan.baidu.com/s/1mcFgL-KvSEzkossR4Rn3TA",
                                        "icon": "fab fa-baidu",
                                        "desc": "适合大文件下载，需登录百度账号"
                                    }
                                ]'>
                            立即下载
                        </button>
                        <button class="purchase-btn" onclick="window.location.href='https://item.taobao.com/item.htm?id=922510812579&spm=a213gs.v2success.0.0.4e724831Wu7MUU'">自助购买</button>
                    </div>
                </div>

                <!-- 搜索结果为空提示 -->
                <div class="search-empty" id="search-empty">
                    <h3>未找到匹配的软件</h3>
                    <p>请尝试使用不同的关键词或检查拼写</p>
                </div>
            </main>
        </div>
    </div>

    <footer>
        <p>© 2023 星际软件库 | 探索未来科技的无限可能</p>
        <p>所有软件均经过量子病毒扫描，安全可靠</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const audioPlayer = document.getElementById('bg-music');

            // 音频播放事件
            audioPlayer.addEventListener('play', function () {
                console.log('背景音乐开始播放');
            });

            // 音频结束事件
            audioPlayer.addEventListener('ended', function () {
                console.log('背景音乐播放完毕');
            });

            // 音频错误事件
            audioPlayer.addEventListener('error', function () {
                console.error('背景音乐加载失败');
            });

            const categoryItems = document.querySelectorAll('.category-item');
            const subcategoryItems = document.querySelectorAll('.subcategory-item');
            const softwareCards = document.querySelectorAll('.software-card');
            const searchEmpty = document.getElementById('search-empty');

            // 初始隐藏空结果提示
            searchEmpty.style.display = 'none';

            // 更新分类计数
            updateCategoryCounts();

            // 二级分类菜单控制 - 修改为点击箭头才展开/收起
            const categoryItemsWithSubmenu = document.querySelectorAll('.category-item.has-submenu');
            categoryItemsWithSubmenu.forEach(item => {
                const arrow = item.querySelector('i');
                
                // 点击箭头时切换子菜单
                arrow.addEventListener('click', function(e) {
                    e.stopPropagation();
                    item.classList.toggle('active');
                });
                
                // 点击分类项本身时不收起子菜单
                item.addEventListener('click', function(e) {
                    if (e.target === this || e.target === this.querySelector('a')) {
                        e.stopPropagation();
                        
                        // 移除所有分类项的active类
                        categoryItems.forEach(i => i.classList.remove('active'));
                        // 为当前点击的分类项添加active类
                        this.classList.add('active');

                        const category = this.getAttribute('data-category');
                        performSearch(category);
                    }
                });
            });

            // 二级分类项点击事件
            subcategoryItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.stopPropagation();
                    
                    // 移除所有分类项的active类
                    categoryItems.forEach(i => i.classList.remove('active'));
                    // 为当前点击的分类项添加active类
                    this.classList.add('active');
                    
                    const category = this.getAttribute('data-category');
                    performSearch(category);
                });
            });

            // 为每个分类项添加点击事件
            categoryItems.forEach(item => {
                // 跳过带子菜单的分类项，因为它们有单独的处理
                if (item.classList.contains('has-submenu')) {
                    return;
                }
                
                item.addEventListener('click', function(e) {
                    // 移除所有分类项的active类
                    categoryItems.forEach(i => i.classList.remove('active'));
                    // 为当前点击的分类项添加active类
                    this.classList.add('active');

                    const category = this.getAttribute('data-category');
                    performSearch(category);
                });
            });

            // 搜索功能 - 实时搜索
            const searchInput = document.getElementById('search-input');
            const searchButton = document.getElementById('search-button');
            let currentCategory = 'all';

            // 实时搜索 - 输入事件
            searchInput.addEventListener('input', function () {
                performSearch(currentCategory);
            });

            // 按钮点击搜索
            searchButton.addEventListener('click', function () {
                performSearch(currentCategory);
            });

            // 按回车键也触发搜索
            searchInput.addEventListener('keypress', function (e) {
                if (e.key === 'Enter') {
                    performSearch(currentCategory);
                }
            });

            // 执行搜索
            function performSearch(category) {
                currentCategory = category;
                const searchTerm = searchInput.value.toLowerCase().trim();

                // 隐藏所有卡片
                softwareCards.forEach(card => {
                    card.style.display = 'none';
                });

                // 根据分类和搜索词过滤卡片
                let visibleCards = 0;
                softwareCards.forEach(card => {
                    const cardCategory = card.getAttribute('data-category');
                    const title = card.querySelector('h3').textContent.toLowerCase();
                    const description = card.querySelector('.software-description').textContent.toLowerCase();

                    // 检查是否匹配分类和搜索词
                    const matchesCategory = category === 'all' || cardCategory === category;
                    const matchesSearch = searchTerm === '' || title.includes(searchTerm) || description.includes(searchTerm);

                    if (matchesCategory && matchesSearch) {
                        card.style.display = 'flex';
                        // 重置动画
                        card.style.animation = 'none';
                        card.offsetHeight; // 触发重绘
                        card.style.animation = 'fadeIn 0.5s forwards';
                        visibleCards++;
                    }
                });

                // 更新分类计数
                updateCategoryCounts();
                
                // 显示或隐藏空结果提示
                if (visibleCards === 0) {
                    searchEmpty.style.display = 'block';
                    // 添加动画
                    searchEmpty.style.animation = 'none';
                    searchEmpty.offsetHeight; // 触发重绘
                    searchEmpty.style.animation = 'fadeIn 0.5s forwards';
                } else {
                    searchEmpty.style.display = 'none';
                }
            }

            // 更新分类计数函数
            function updateCategoryCounts() {
                const categoryCount = {};
                const subcategoryCount = {};
                
                // 初始化所有分类计数
                categoryItems.forEach(item => {
                    const category = item.getAttribute('data-category');
                    categoryCount[category] = 0;
                });
                
                // 初始化所有子分类计数
                subcategoryItems.forEach(item => {
                    const subcategory = item.getAttribute('data-category');
                    subcategoryCount[subcategory] = 0;
                });
                
                // 计算每个分类的软件数量
                softwareCards.forEach(card => {
                    const cardCategory = card.getAttribute('data-category');
                    
                    // 更新主分类计数
                    if (categoryCount[cardCategory] !== undefined) {
                        categoryCount[cardCategory]++;
                    }
                    
                    // 更新子分类计数
                    if (subcategoryCount[cardCategory] !== undefined) {
                        subcategoryCount[cardCategory]++;
                    }
                });
                
                // 全部软件计数
                categoryCount['all'] = softwareCards.length;
                
                // 更新主分类徽章的数量显示
                categoryItems.forEach(item => {
                    const category = item.getAttribute('data-category');
                    const badge = item.querySelector('.category-badge');
                    const count = categoryCount[category] || 0;
                    badge.textContent = `${count}个`;
                });
                
                // 更新子分类徽章的数量显示
                subcategoryItems.forEach(item => {
                    const subcategory = item.getAttribute('data-category');
                    const badge = item.querySelector('.category-badge');
                    const count = subcategoryCount[subcategory] || 0;
                    badge.textContent = `${count}个`;
                });
            }

            // 新增的二维码弹窗控制代码
            const aboutBtn = document.getElementById('aboutBtn');
            const qrModal = document.getElementById('qrModal');
            const qrClose = document.getElementById('qrClose');

            // 点击关于我们按钮显示二维码弹窗
            aboutBtn.addEventListener('click', function () {
                qrModal.classList.add('active');
            });

            // 点击关闭按钮隐藏二维码弹窗
            qrClose.addEventListener('click', function () {
                qrModal.classList.remove('active');
            });

            // 点击弹窗外部也隐藏二维码弹窗
            qrModal.addEventListener('click', function (e) {
                if (e.target === qrModal) {
                    qrModal.classList.remove('active');
                }
            });

            // 进入弹窗公告控制代码
            const announcementModal = document.getElementById('announcementModal');
            const announcementClose = document.getElementById('announcementClose');
            const announcementConfirm = document.getElementById('announcementConfirm');

            // 页面加载完成后显示进入弹窗公告
            announcementModal.classList.add('active');

            // 点击关闭按钮隐藏进入弹窗公告
            announcementClose.addEventListener('click', function () {
                announcementModal.classList.remove('active');
            });

            // 点击确认按钮隐藏进入弹窗公告
            announcementConfirm.addEventListener('click', function () {
                announcementModal.classList.remove('active');
            });

            // 点击弹窗外部也隐藏进入弹窗公告
            announcementModal.addEventListener('click', function (e) {
                if (e.target === announcementModal) {
                    announcementModal.classList.remove('active');
                }
            });

            // 下载选项弹窗控制代码
            const downloadModal = document.getElementById('downloadModal');
            const downloadClose = document.getElementById('downloadClose');
            const downloadCancel = document.getElementById('downloadCancel');
            const downloadOptions = document.getElementById('downloadOptions');
            const downloadTitle = document.getElementById('downloadTitle');
            const downloadButtons = document.querySelectorAll('.download-btn');

            // 为所有下载按钮添加点击事件
            downloadButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const softwareName = this.getAttribute('data-name');
                    const options = JSON.parse(this.getAttribute('data-options') || '[]');
                    
                    // 清空之前的选项
                    downloadOptions.innerHTML = '';
                    
                    // 设置弹窗标题
                    downloadTitle.textContent = `${softwareName} - 下载选项`;
                    
                    // 添加下载选项
                    options.forEach(option => {
                        addDownloadOption(option.name, option.url, option.icon, option.desc);
                    });
                    
                    // 显示弹窗
                    downloadModal.classList.add('active');
                });
            });

            // 添加下载选项函数
            function addDownloadOption(name, url, iconClass, desc) {
                const option = document.createElement('div');
                option.className = 'download-option';
                option.innerHTML = `
                    <div class="download-option-icon">
                        <i class="${iconClass}"></i>
                    </div>
                    <div class="download-option-text">
                        <div class="download-option-name">${name}</div>
                        <div class="download-option-desc">${desc}</div>
                    </div>
                    <div class="download-option-buttons">
                        <button class="download-copy-btn" data-url="${url}">复制</button>
                        <button class="download-now-btn" data-url="${url}">下载</button>
                    </div>
                `;
                
                // 添加点击事件 - 直接下载
                const downloadBtn = option.querySelector('.download-now-btn');
                downloadBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    window.location.href = url;
                });
                
                downloadOptions.appendChild(option);
            }

            // 点击关闭按钮隐藏下载弹窗
            downloadClose.addEventListener('click', function () {
                downloadModal.classList.remove('active');
            });

            // 点击取消按钮隐藏下载弹窗
            downloadCancel.addEventListener('click', function () {
                downloadModal.classList.remove('active');
            });

            // 点击弹窗外部也隐藏下载弹窗
            downloadModal.addEventListener('click', function (e) {
                if (e.target === downloadModal) {
                    downloadModal.classList.remove('active');
                }
            });

            // 复制下载链接功能
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('download-copy-btn')) {
                    e.stopPropagation(); // 阻止事件冒泡
                    const url = e.target.getAttribute('data-url');
                    navigator.clipboard.writeText(url).then(() => {
                        const originalText = e.target.textContent;
                        e.target.textContent = '已复制';
                        setTimeout(() => {
                            e.target.textContent = originalText;
                        }, 2000);
                    });
                }
            });
            
            // 初始化时自动显示第一分类的软件数量
            updateCategoryCounts();
        });
    </script>
</body>

</html>