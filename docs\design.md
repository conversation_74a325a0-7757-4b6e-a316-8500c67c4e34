# 娜宝贝软件系统设计文档

本文档详细说明系统架构、文件结构和代码设计，供开发人员参考和维护。

## 系统架构

### 前后端结构

本系统采用前后端分离的架构设计：

- 前端：基于HTML、CSS和JavaScript，通过API与后端交互
- 后端：纯PHP编写的RESTful API，使用SQLite数据库存储数据

### PHP版本兼容性

本系统设计为兼容 PHP 7.4 及以上版本：

- 避免使用 PHP 8.0+ 的新特性，如空值合并运算符与异常抛出的组合 (`$var ?? throw new Exception()`)
- 使用 PHP 7.4 兼容的语法进行错误检查和异常处理
- 所有条件检查使用传统的 `if (!isset($var))` 结构而非 PHP 8.0 的新语法
- 确保所有代码在 PHP 7.4 环境中可正常运行

### 目录结构

```
/
├── docs/                   # 文档目录
│   ├── deployment.md       # 部署文档
│   └── design.md           # 设计文档
└── wwwroot/                # 网站根目录
    ├── site_settings.php   # 站点设置文件（包含所有配置）
    ├── config.php          # 前台配置文件
    ├── database/           # 数据库文件目录
    │   └── nbbrj.db        # SQLite数据库文件
    ├── uploads/            # 上传文件目录
    │   ├── software/       # 软件文件存储目录
    │   ├── icons/          # 图标文件存储目录
    │   └── site/           # 站点图片存储目录
    │       └── qrcode/     # 二维码图片目录
    ├── admin/              # 管理后台
    │   ├── config.php      # 管理后台配置文件
    │   ├── common.php      # 管理后台公共函数库
    │   ├── index.php       # 管理后台入口文件
    │   └── panel/          # 管理面板页面
    │       ├── dashboard.php  # 仪表盘页面
    │       ├── category.php   # 分类管理页面
    │       ├── software.php   # 软件管理页面
    │       ├── admin.php      # 管理员管理页面
    │       ├── roles.php      # 角色管理页面
    │       └── settings.php   # 站点设置页面
    ├── api/                # API接口
    │   ├── config.php      # API配置文件
    │   ├── common.php      # API公共函数库
    │   ├── admin/          # 管理API
    │   │   ├── panel.php   # 管理员验证文件
    │   │   ├── public/     # 公共管理API（无需登录）
    │   │   │   ├── login.php  # 登录接口
    │   │   │   └── logout.php # 退出接口
    │   │   └── panel/      # 管理面板API（需要登录）
    │   │       ├── software.php   # 软件管理接口
    │   │       ├── category.php   # 分类管理接口
    │   │       ├── admin.php      # 管理员管理接口
    │   │       ├── roles.php      # 角色管理接口
    │   │       ├── upload.php     # 文件上传接口
    │   │       └── settings.php   # 站点设置接口
    │   └── public/         # 公共API
    │       ├── install.php     # 安装接口
    │       ├── index.php       # 软件列表和分类接口
    │       ├── download.php    # 软件下载接口
    │       ├── payment.php     # 支付接口
    │       └── payment_notify.php # 支付回调通知接口
    ├── assets/             # 静态资源
    │   ├── css/            # CSS文件
    │   ├── js/             # JavaScript文件
    │   │   ├── api.js      # API工具函数
    │   │   ├── software.js # 软件相关功能
    │   │   └── payment.js  # 支付相关功能
    │   └── img/            # 图片文件
    ├── common.php          # 前台公共函数库
    ├── index.html          # 前台首页
    └── install.php         # 安装页面
```

## 项目组件

系统由三个完全独立的组件组成，每个组件都有自己的配置文件和公共函数库，不应相互引用：

1. **管理后台前端** (`/wwwroot/admin/`)
   - 配置文件：`config.php`
   - 公共函数库：`common.php`
   - 入口文件：`index.php`

2. **公共前端** (`/wwwroot/`)
   - 配置文件：`config.php`
   - 公共函数库：`common.php`
   - 入口文件：`index.html`

3. **API后端** (`/wwwroot/api/`)
   - 配置文件：`config.php`
   - 公共函数库：`common.php`
   - 管理API：`/admin/`
   - 公共API：`/public/`

所有组件共享一个站点设置文件 `site_settings.php`，该文件包含所有可配置的站点设置，如站点标题、数据库路径、存储路径等。该文件存储相对于网站根目录的路径、可配置的存储路径以及安装时间戳（`installed_time`）。每个组件的配置文件中都包含指向这个共享设置文件的路径。

## 安全性设计

### 路径安全

系统使用了以下方式确保文件包含和路径引用的安全性：

1. **目录分隔符常量**：
   - 使用 `DS` 常量代替直接使用 `/` 或 `\`，确保跨平台兼容性
   - 所有文件都必须在顶部定义目录分隔符常量：`if (!defined('DS')) { define('DS', DIRECTORY_SEPARATOR); }`
   - 所有路径构建都使用 `dirname(__FILE__)` 或 `__DIR__` 而非相对路径

2. **安全措施**：
   - 所有的 `require` 和 `include` 必须使用 `__DIR__` 或绝对路径
   - 防止直接访问核心文件的安全检查函数
   - 遵循最小权限原则，限制目录访问权限

### 示例用法

系统中正确的文件包含和路径构建方式：

- 使用 `__DIR__` 或 `dirname(__DIR__)` 获取当前文件或上级目录的绝对路径
- 使用 `DS` 常量作为目录分隔符，确保跨平台兼容性
- 配置文件加载使用绝对路径，如 `dirname(__DIR__) . DS . 'site_settings.php'`
- 存储路径构建使用绝对路径，如 `dirname(__DIR__) . DS . $config['upload']['software_package_path']`

错误的做法（应避免）：
- 使用相对路径如 `../common.php` 或 `./inc/functions.php`
- 直接拼接相对路径如 `../uploads/software`
- 混合使用相对路径和配置值如 `'../' . $config['basic']['icons_storage_path']`

## 数据库设计

系统使用SQLite数据库，包含以下表结构：

### 表结构

1. **admins** - 管理员表
   - `id`: INTEGER, 主键，自增
   - `username`: TEXT, 用户名，唯一
   - `password`: TEXT, 密码哈希
   - `salt`: TEXT, 密码盐值
   - `last_login`: INTEGER, 最后登录时间
   - `created_at`: INTEGER, 创建时间
   - `role_id`: INTEGER, 角色ID，默认为1（超级管理员）

2. **admin_roles** - 管理员角色表
   - `id`: INTEGER, 主键，自增
   - `name`: TEXT, 角色名称，唯一
   - `permissions`: TEXT, 权限JSON字符串
   - `description`: TEXT, 角色描述
   - `created_at`: INTEGER, 创建时间
   - `updated_at`: INTEGER, 更新时间

3. **categories** - 分类表
   - `id`: INTEGER, 主键，自增
   - `name`: TEXT, 分类名称
   - `sort`: INTEGER, 排序顺序，默认为0
   - `created_at`: INTEGER, 创建时间

4. **softwares** - 软件表
   - `id`: INTEGER, 主键，自增
   - `name`: TEXT, 软件名称
   - `category`: INTEGER, 所属分类ID，外键关联categories表
   - `icon`: TEXT, 图标URL
   - `description`: TEXT, 软件描述
   - `version`: TEXT, 版本号
   - `size`: TEXT, 大小
   - `downloads`: INTEGER, 下载次数，默认为0
   - `fake_downloads`: TEXT, 虚拟下载量（展示用）
   - `price`: TEXT, 价格信息
   - `video_url`: TEXT, 视频URL
   - `download_url_1`: TEXT, 下载URL 1（本地下载）
   - `download_url_2`: TEXT, 下载URL 2（备用下载）
   - `download_url_3`: TEXT, 下载URL 3（百度网盘）
   - `download_url_4`: TEXT, 下载URL 4（预留扩展）
   - `download_url_5`: TEXT, 下载URL 5（预留扩展）
   - `buy_url`: TEXT, 购买链接
   - `sort`: INTEGER, 排序顺序，默认为0
   - `created_at`: INTEGER, 创建时间

5. **download_logs** - 下载日志表
   - `id`: INTEGER, 主键，自增
   - `software_id`: INTEGER, 软件ID，外键关联softwares表
   - `ip`: TEXT, 下载者IP
   - `user_agent`: TEXT, 浏览器用户代理
   - `referer`: TEXT, 来源页面
   - `created_at`: INTEGER, 创建时间

6. **payment_orders** - 订单表
   - `id`: INTEGER, 主键，自增
   - `order_no`: TEXT, 订单号，唯一
   - `software_id`: INTEGER, 软件ID，外键关联softwares表
   - `amount`: REAL, 订单金额
   - `payment_type`: TEXT, 支付类型（wechat_pay, alipay）
   - `status`: TEXT, 订单状态（pending, paid, failed）
   - `user_ip`: TEXT, 用户IP
   - `transaction_id`: TEXT, 交易ID
   - `created_at`: INTEGER, 创建时间
   - `paid_at`: INTEGER, 支付时间

7. **payment_notify_logs** - 支付通知记录表
   - `id`: INTEGER, 主键，自增
   - `payment_type`: TEXT, 支付类型（wechat_pay, alipay）
   - `raw_data`: TEXT, 原始通知数据（始终使用原始数据重新处理，确保数据一致性）
   - `order_no`: TEXT, 订单号（如果能解析出来）
   - `signature_valid`: INTEGER, 签名验证结果（1=成功，0=失败，-1=未验证）
   - `signature_error`: TEXT, 签名验证错误信息
   - `processing_result`: TEXT, 处理结果（success, failed, error）
   - `processing_error`: TEXT, 处理错误信息
   - `ip_address`: TEXT, 请求IP地址
   - `request_line`: TEXT, HTTP请求行（格式：METHOD URI PROTOCOL，如"POST /api/payment_notify.php?type=alipay HTTP/1.1"）
   - `request_headers`: TEXT, 请求头信息（JSON格式，包含Cookie、User-Agent、Content-Type等所有HTTP头）
   - `server_info`: TEXT, 服务器环境信息（JSON格式）
   - `created_at`: INTEGER, 创建时间

## 文件存储功能

系统实现了本地文件存储功能，用于存储软件文件和图标：

1. **存储路径配置**：
   - 在站点设置中配置软件文件和图标的存储路径
   - 默认路径分别为 `uploads/software` 和 `uploads/icons`
   - 路径可以通过管理面板的站点设置页面修改

2. **文件上传功能**：
   - 支持软件文件上传：ZIP、RAR、7Z、EXE、MSI等格式
   - 支持图标上传：JPEG、PNG、GIF、WEBP等格式
   - 上传文件自动重命名，防止文件名冲突
   - 上传成功后自动更新数据库中的URL和文件大小

3. **自动保存功能**：
   - 编辑软件时上传文件会自动保存到数据库
   - 防止忘记保存导致的重复上传和冗余文件
   - 上传成功后显示提示，告知用户已自动保存

4. **文件下载功能**：
   - 支持本地文件下载和外部链接重定向
   - 自动检测文件类型并设置正确的MIME类型
   - 流式下载，提高下载效率
   - 记录下载日志，包括IP、用户代理等信息

## 系统安装检查机制

系统实现了一套安装检查机制，确保数据库和配置文件存在并正确设置：

1. **安装检查流程**：
   - API后端负责安装检查逻辑
   - `common.php` 中的 `check_installed()` 函数检查站点设置文件和数据库文件是否存在
   - 如果任一文件不存在，将自动重定向到安装页面

2. **自动重定向**：
   - API接口：返回状态码 303，并在响应中包含安装页面URL
   - 前端JS检测到特定状态码后自动重定向到安装页面

3. **安装页面内容**：
   - 数据库路径设置：安装时会自动转换为绝对路径存储
   - 站点信息设置：网站标题、副标题等
   - 管理员账号设置：用户名和密码
   - 自动检测前端URL，用于支付回调等功能
   - 安装后自动创建站点设置文件和数据库文件

4. **配置文件生成**：
   - 安装过程会在 `/wwwroot/site_settings.php` 中创建站点设置
   - 设置文件包含数据库绝对路径、站点信息、安装时间戳和管理员账号
   - 各组件的配置文件（`config.php`）已预先创建，指向站点设置文件
   - 配置文件中不包含冗余数据结构，完全信任 `site_settings.php` 的结构

## 身份认证机制

系统提供两种身份认证机制：服务端认证（API）和客户端认证（管理后台）：

### 服务端认证（API）

API接口使用PHP会话（Session）进行身份认证：

1. **登录流程**：
   - 登录API（`/api/admin/public/login.php`）验证用户凭据
   - 验证成功后创建会话并返回用户信息
   - API返回成功状态和用户数据

2. **权限验证**：
   - API接口在执行前验证会话状态
   - 未授权请求返回401状态码
   - 权限不足返回403状态码

3. **注销流程**：
   - 退出API（`/api/admin/public/logout.php`）销毁会话
   - 返回成功状态

### 客户端认证（管理后台）

管理后台使用客户端存储（localStorage）进行身份认证：

1. **登录流程**：
   - 用户在登录页面（`/admin/public/login.php`）输入凭据
   - 表单提交到登录API
   - 登录成功后，将用户信息存储在localStorage中：
     - `admin_id`: 用户ID
     - `admin_username`: 用户名
     - `admin_login_time`: 登录时间戳
     - `admin_permissions`: 权限列表

2. **权限验证**：
   - 页面加载时通过JavaScript检查localStorage中的登录状态
   - 检查登录时间是否超过配置的超时时间
   - 验证用户是否具有访问页面所需的权限
   - 未登录或权限不足时重定向到相应页面

3. **注销流程**：
   - 用户点击"退出"按钮
   - 清除localStorage中的所有认证数据
   - 重定向到登录页面

## 管理面板公共模块

系统实现了一个管理面板公共模块，简化页面开发流程并确保生成静态HTML：

1. **公共文件 `panel.php`**：
   - 所有管理面板页面（`/admin/panel/`目录下的文件）都会包含此文件
   - 提供页面渲染函数：`render_header()` 和 `render_footer()`
   - 加载配置文件并设置常用变量：站点标题、基础路径等
   - 使用预定义路径常量确保路径引用安全
   - 不包含任何动态操作，仅生成静态HTML

2. **客户端权限检查**：
   - `render_permission_check()`: 生成JavaScript代码检查权限
   - 支持单个权限检查或多个权限的"任一"检查模式
   - 权限不足时通过JavaScript重定向到 `unauthorized.html` 页面
   - 所有权限检查在客户端完成，不依赖服务器会话

3. **配置加载**：
   - 从 `config.php` 加载基础路径和API路径配置
   - 所有链接和API调用使用配置的路径，而非硬编码值
   - 支持自定义会话超时时间
   - 当配置文件缺失时，显示错误信息而非使用硬编码的备用配置

4. **页面渲染**：
   - `render_header()`: 渲染页面头部，包括导航栏和侧边栏
   - `render_footer()`: 渲染页面底部，包括通用JavaScript代码
   - 渲染函数可以加载站点设置数据并生成HTML
   - 不执行HTML本身不支持的动作，如操作header、读写会话或服务器端重定向
   - 菜单项通过JavaScript动态加载，支持权限控制
   - 静态资源（JS、CSS）通过 `$adminConfig['assets_path']` 引用，而页面URL通过 `$basePath` 引用

## 站点设置管理

系统提供完整的站点设置管理功能，允许管理员在后台界面修改配置：

1. **设置界面**：
   - 位于管理后台的站点设置页面（`/admin/panel/settings.php`）
   - 提供表单界面修改站点标题、副标题等信息
   - 允许更改数据库路径（需要谨慎使用）
   - 使用标签页界面整合多个设置组
   - 包含支付设置（微信支付和支付宝）：
     - 支付功能启用/禁用开关
     - 微信支付配置：AppID、商户ID、API密钥等
     - 支付宝配置：AppID、私钥、公钥等
     - 支付回调URL自动生成，基于前台网站路径
   - 包含上传设置：
     - 软件、图标和站点图片的上传路径配置
     - 各类型文件的最大上传大小限制
     - 允许的文件扩展名配置
   - 支持前台网站路径配置，用于预览上传的图片和生成支付回调URL
   - 智能设置管理功能：
     - 在UI上显示哪些值已被修改（不是默认值）
     - 提交表单时只提交已修改的值，未修改的值不提交
     - 提供还原为默认值的功能（每个字段单独还原）
     - 提供撤销所有修改的功能（恢复到加载时的状态）
     - 使用不同颜色标识字段状态：
       - 黄色：已修改但未保存的值
       - 蓝色：已保存的自定义值（非默认值）
       - 绿色：默认值

2. **设置API接口**：
   - **统一接口**：
     - 使用 `/api/admin/panel/settings.php` 处理所有设置相关操作
     - 支持 GET 请求获取设置数据和 POST 请求保存设置数据
     - 必须通过 `tab` 参数指定要获取或保存的选项卡

   - **数据结构**：
     - 返回的数据按选项卡->分组->字段的层次结构组织
     - 每个字段包含 `value` 属性，表示当前值
     - 如果字段在 `site_settings.php` 中存在，则使用该值（被视为自定义值）
     - 如果字段在 `site_settings.php` 中不存在，则使用默认值

   - **默认值处理**：
     - 判断字段是否为默认值时，只检查字段是否在 `site_settings.php` 中存在
     - 不比较值是否与默认值相等，因为默认值可能会随系统更新而变化
     - 前端使用不同颜色标识字段状态：
       - 黄色：已修改但未保存的值
       - 蓝色：已保存的自定义值（非默认值）
       - 绿色：默认值

   - **还原为默认值**：
     - 前端通过 `_reset_to_defaults` 参数发送需要还原为默认值的字段列表
     - 后端从 `site_settings.php` 中删除这些字段，使其恢复为默认值
     - 支持单个字段还原和整个表单还原

   - **数据提交优化**：
     - 前端只提交已修改的字段，未修改的字段不提交
     - 减少数据传输量，提高性能

   - **文件上传集成**：
     - 支持通过 `/api/admin/panel/settings_upload.php` 上传站点图片
     - 使用固定文件名或随机名称存储站点图片
     - 上传成功后返回文件URL，前端自动更新对应字段值

   - **安全与备份**：
     - API 验证数据有效性，确保数据格式正确
     - 保存前自动创建 `site_settings.php` 的备份文件
     - 备份文件命名格式为 `site_settings.php.bak.YYYYMMDDHHmmss`

   - **文件上传**：
     - 支持通过 `/api/admin/panel/settings_upload.php` 上传站点图片
     - 使用固定文件名而非随机名称存储站点图片
     - 上传成功后返回文件URL，前端自动更新对应字段值

3. **配置文件结构**：
   - 保持PHP数组格式，易于读写
   - 包含注释说明每个配置项的用途
   - 自动备份机制，防止修改出错
   - 站点配置支持多个设置组，按照二级层次结构组织
   - 使用 'title' 作为显示标签和 'name' 作为标识符

## 前端架构

### 1. 静态HTML与API交互

前端采用静态HTML页面与后端API交互的方式：

- 前台页面可以加载站点设置数据并生成HTML
- 不执行HTML本身不支持的动作，如操作header、读写会话或服务器端重定向
- 通过API获取动态数据，如软件列表、分类等
- 使用统一的API工具函数处理请求和响应
- 支持API重定向和错误处理

### 2. 软件显示与购买逻辑

软件卡片显示逻辑：

- 价格格式化：价格大于0时显示为"X.XX元"格式（保留两位小数）
- 按钮显示逻辑：
  - 价格为0或已购买：显示"下载选项"按钮
  - 价格大于0且未购买：显示"自助购买"按钮
- 下载选项弹窗中包含所有可用的下载方式（官方下载、百度网盘等）
- 自助购买按钮点击后显示支付选择弹窗

### 3. API工具函数

系统实现了统一的API工具函数，简化API调用并提供默认的加载状态管理。这些函数位于 `/wwwroot/assets/js/api.js`，包括：

- `apiGet(url, data, options, messageOptions)`: 发送GET请求
- `apiPost(url, data, options, messageOptions)`: 发送POST请求
- `apiPut(url, data, options, messageOptions)`: 发送PUT请求
- `apiDelete(url, data, options, messageOptions)`: 发送DELETE请求
- `showMessage(data, successMessage, operation)`: 处理API响应消息显示

#### API加载状态管理

系统为所有API调用提供了默认的加载遮罩功能：

1. **默认行为**：
   - 所有API调用默认显示全屏加载遮罩和"加载中"提示
   - 不同类型的请求有不同的默认提示文本：
     - GET请求：`加载中...`
     - POST请求：`提交中...`
     - PUT请求：`更新中...`
     - DELETE请求：`删除中...`

2. **自定义选项**：
   - `showLoading`: 是否显示加载遮罩（默认为true）
   - `loadingMessage`: 自定义加载提示文本

3. **全局控制函数**：
   - `showLoading(message)`: 手动显示加载遮罩
   - `hideLoading()`: 手动隐藏加载遮罩
   - `forceHideLoading()`: 强制隐藏加载遮罩（忽略计数器）

4. **使用示例**：
   ```javascript
   // 默认显示加载遮罩
   const data = await apiGet('/api/data');

   // 自定义加载文本
   const result = await apiPost('/api/save', formData, {}, {
       loadingMessage: '正在保存数据...',
       showSuccess: true
   });

   // 禁用加载遮罩
   const quickData = await apiGet('/api/quick-check', {}, {}, {
       showLoading: false
   });
   ```

这些函数封装了fetch API，确保所有请求都包含正确的头信息，并统一处理错误、重定向、消息提示和加载状态。所有API函数都使用统一的参数格式，便于使用：
- `url`: 请求的URL地址
- `data`: 请求的数据（GET请求中作为查询参数，POST/PUT请求中作为请求体）
- `options`: 额外的请求选项（如headers等）
- `messageOptions`: 消息处理选项（如showSuccess, successMessage, operation, showLoading, loadingMessage等）

### 4. URL路由功能

系统支持通过URL hash参数直接访问特定软件的下载选项：

- **软件直链格式**：`/#/software/{软件ID}`
- **功能特性**：
  - 页面加载时自动检测URL中的hash参数
  - 如果匹配软件直链格式，自动获取软件信息并显示下载选项弹窗
  - 在软件直链访问时不显示公告弹窗，提供更好的用户体验
  - 支持浏览器前进后退功能，监听hash变化事件
  - 自动处理软件不存在的情况，显示友好的错误提示

- **实现机制**：
  - 前端通过正则表达式解析hash参数：`/^#\/software\/(\d+)$/`
  - 调用API获取单个软件信息：`/api/public/index.php?software_id={id}`
  - Vue实例通过全局变量暴露，支持外部调用下载选项弹窗
  - 延迟处理确保Vue实例完全初始化



### 5. 文件组织

前端静态资源组织在 `/assets/` 目录下：

- CSS文件：`/assets/css/`
- JavaScript文件：`/assets/js/`
- 图片文件：`/assets/img/`

JavaScript文件按功能模块划分：

- `api.js`：API工具函数
- `software.js`：软件相关功能
- `payment.js`：支付相关功能

## 配置管理

系统明确区分两种不同的配置概念：

### Config 与 Setting 的区别

1. **Config（组件配置）**：
   - 指各个组件（前台、管理后台、API）的基础配置
   - 包含路径配置、调试模式、API版本等技术性配置
   - 存储在各组件的 `config.php` 文件中
   - 通过 `get_config()` 函数获取
   - 通常在部署时设置，运行时很少修改

2. **Setting（站点设置）**：
   - 指站点的业务设置和用户可配置的选项
   - 包含站点标题、支付配置、上传设置等业务性配置
   - 存储在共享的 `site_settings.php` 文件中
   - 通过 `get_settings()` 函数获取
   - 可以通过管理后台界面进行修改

### 函数使用规范

- `get_config()`：获取当前组件的基础配置信息
- `get_settings()`：获取站点的业务设置信息
- 数据库连接、支付配置等业务功能应使用 `get_settings()`
- 路径配置、组件调试模式等技术配置应使用 `get_config()`

### 调试模式的区别

系统有两种不同的调试模式：

1. **组件调试模式**（`config.php` 中的 `debug`）：
   - 用于技术性调试，如显示前端调试信息、API错误详情等
   - 每个组件独立配置，互不影响
   - 通过 `get_config()` 获取，如 `$config['debug']`
   - 适用于开发和部署阶段的技术调试
   - **使用规范**：所有API接口的技术性调试日志都应使用 `$config['debug']` 来控制

2. **业务调试设置**（`site_settings.php` 中的 `debug` 组）：
   - 用于业务功能的调试，如跳过支付签名验证、记录支付回调等
   - 全站共享配置，影响所有组件的业务行为
   - 通过 `get_settings()` 获取，如 `$settings['debug']['skip_payment_signature_verification']`
   - 适用于生产环境的业务功能调试

系统使用多个配置文件管理不同组件的配置，确保各组件完全独立：

### 1. 设置管理架构

系统采用分离的设置管理架构，提高代码的可维护性和解耦性：

#### 配置默认值和相对路径处理

1. **默认值处理**：
   - 所有配置项的默认值都在 `Settings.php` 中定义
   - 安装时只需设置必要的配置项，其他配置项使用默认值
   - 安装脚本 `install.php` 只保留必要的参数：
     - `installed`: 安装状态
     - `basic.site_title`: 站点标题
     - `basic.site_subtitle`: 站点副标题
     - `basic.frontend_url`: 前台网站路径
     - `db_path`: 数据库路径
     - `installed_time`: 安装时间
     - `admin`: 管理员信息（用于救援模式）
   - 更新配置时保留现有的其他设置，只更新必要的参数
   - 这样可以简化安装过程，减少配置文件的复杂度

2. **相对路径处理**：
   - 支付回调URL等配置项使用相对路径（如 `api/public/payment_notify.php?type=wechat_pay`）
   - 在使用时自动添加 `frontend_url` 前缀
   - 这样可以简化配置，避免硬编码完整URL
   - 在 `get_payment_config()` 函数中自动处理相对路径

3. **前端URL获取**：
   - 安装时通过JavaScript自动获取当前URL作为前端URL
   - 用户可以在安装界面查看和修改自动检测的URL
   - 避免后端通过 `$_SERVER` 获取URL可能不准确的问题
   - 特别适用于使用代理、负载均衡或HTTPS终止的环境

- **Settings 类**（位于 `includes/Settings.php`）：
  - 定义和管理站点设置的结构、默认值和验证规则
  - 被所有三个子项目共享使用，提供统一的设置管理接口
  - 负责加载和保存 `site_settings.php` 文件
  - 提供 `load()`、`save()`、`get()`、`set()` 等核心方法
  - 支持任意级别的嵌套设置，使用点号分隔的路径格式（如 `payment.wechat_pay.enabled`）
  - 包含 `getDefaults()`、`getDefault()` 等默认值管理方法
  - 提供 `validateValue()`、`stringToArray()`、`arrayToString()` 等工具方法
  - 提供 `getKeys()` 和 `getNestedKeys()` 方法获取设置键名
  - 包含 `processUploadSettings()` 方法处理上传设置的特殊需求
  - 支持根级别设置的获取和验证
  - **智能验证器系统**：从配置文件的现有属性中推断验证规则，不包含硬编码的验证值
    - `loadValidationRules()` 方法从 `settings_tabs_*.php` 配置文件中遍历字段配置
    - `inferValidationRule()` 方法根据字段属性智能推断验证规则：
      - `required=true` + `type=text` → `required_string` 验证
      - `type=checkbox` 或 `valueType=boolean` → `boolean` 验证
      - `type=number` + `min/max/step` → `numeric_range` 验证（自动提取min/max/step值）
      - 存在 `options` 数组 → `enum` 验证（自动提取allowed_values）
      - 字段名包含 `path` → `path` 验证
      - 字段名包含 `extensions` → `array` 验证
      - 字段名包含 `max_size` → `numeric_range` 验证（min=0）
    - `createValidator()` 方法根据推断的验证规则动态创建验证器函数
    - `setNestedValidator()` 方法处理嵌套路径的验证器设置
  - 提供 `validateBoolean()`、`validatePath()`、`validatePaymentProvider()` 等通用验证方法
  - 保留向后兼容的旧方法（`getOld()`、`getDefaultOld()`、`validateValueOld()`）

- **admin_settings_tabs 配置**（位于 `api/admin/config/settings_tabs*.php`）：
  - 仅负责后台设置的布局、分组和分类
  - 不包含具体参数的默认值，只定义UI结构
  - 使用 `settings_path` 属性引用 Settings 类中的设置路径
  - 每个字段的 `name` 属性使用完整的设置路径（如 `basic.site_title`、`payment.wechat_pay.enabled`）
  - 字段的 `condition` 属性也使用完整的设置路径（如 `payment.enabled`、`announcement.enabled`）
  - 完全独立于 Settings 类，只负责UI展示和交互
  - 每个字段包含 `name`、`label`、`type` 等UI相关属性
  - **智能验证规则推断**：Settings 类会自动从字段的现有属性中推断验证规则
    - 无需额外的 `validation` 属性，避免重复配置
    - 从 `type`、`required`、`min`、`max`、`step`、`options`、`valueType` 等属性推断验证类型
    - 从字段名称模式（如包含 `path`、`extensions`、`max_size`）推断特殊验证需求
    - 保持配置文件简洁，减少维护成本
  - **每个选项卡对应一个单独的配置文件**，文件命名规则为 `settings_tabs_[选项卡名称].php`：
    - `settings_tabs_website.php` - 网站设置选项卡
    - `settings_tabs_list.php` - 列表设置选项卡
    - `settings_tabs_system.php` - 系统设置选项卡
    - `settings_tabs_payment.php` - 支付设置选项卡
    - `settings_tabs_upload.php` - 上传设置选项卡
  - **中央加载器**：`settings_tabs.php` 提供 `load_settings_tabs()` 函数，负责加载和合并所有选项卡配置文件
  - 通过 API 接口 `api/admin/panel/settings_tabs.php` 获取，支持获取所有选项卡列表或单个选项卡的详细信息
  - API接口使用中央加载器加载所有配置文件，确保前端能够获取完整的选项卡列表

这种分离使得代码更加清晰、可维护，并避免了重复定义默认值。前端界面通过 Settings 类获取默认值，而不是在UI配置中硬编码。

### 2. 配置文件结构

系统有三个主要组件，每个组件都有自己独立的配置文件：

1. **前台配置文件** (`/wwwroot/config.php`)：
前台配置文件 (`/wwwroot/config.php`) 包含以下主要配置项：
- 站点设置文件路径 (`site_settings_path`)
- 前台基础路径 (`base_path`)
- API路径 (`api_path`)
- 资源路径 (`assets_path`)
- 调试模式开关 (`debug`)

2. **管理后台配置文件** (`/wwwroot/admin/config.php`)：
管理后台配置文件 (`/wwwroot/admin/config.php`) 包含以下主要配置项：
- 站点设置文件路径 (`site_settings_path`)
- 管理后台基础路径 (`base_path`)
- 管理后台API路径 (`api_path`)
- 资源路径 (`assets_path`)
- 默认页面标题 (`default_title`)
- 默认主题颜色 (`theme_color`)
- 会话超时时间 (`session_timeout`)
- 调试模式开关 (`debug`)

3. **API配置文件** (`/wwwroot/api/config.php`)：
API配置文件 (`/wwwroot/api/config.php`) 包含以下主要配置项：
- 站点设置文件路径 (`site_settings_path`)
- API版本 (`api_version`)
- 允许的跨域来源 (`allowed_origins`)
- API请求限制 (`rate_limit`)
- 调试模式开关 (`debug`)

### 2. 站点设置文件

所有组件共享一个站点设置文件 (`/wwwroot/site_settings.php`)，存储所有可配置的站点设置。该文件包含相对于网站根目录的路径、可配置的存储路径以及安装时间戳等信息：

站点设置文件 (`/wwwroot/site_settings.php`) 包含以下主要配置组：

- **安装信息**：
  - `installed`: 安装状态
  - `installed_time`: 安装时间戳

- **基本设置** (`basic`)：
  - `site_title`: 站点标题
  - `site_subtitle`: 站点副标题
  - `frontend_url`: 前台网站路径（用于支付回调等功能）

- **数据库设置**：
  - `db_path`: 数据库文件的绝对路径

- **管理员信息** (`admin`)：
  - `username`: 管理员用户名
  - `password`: 救援模式明文密码（使用后自动清空）

- **分页设置** (`pagination`)：
  - `default_page_size`: 默认每页显示数量
  - `max_page_size`: 最大每页显示数量
  - `show_total`: 是否显示总数

- **搜索设置** (`search`)：
  - `mode`: 搜索模式（即时或点击）
  - `delay`: 即时搜索延迟时间

- **公告设置** (`announcement`)：
  - `enabled`: 是否启用公告
  - `title`: 公告标题
  - `content`: 公告内容
  - `style`: 公告样式
  - `repeat_show`: 是否重复显示
  - `timestamp`: 公告时间戳

- **支付设置** (`payment`)：
  - `enabled`: 是否启用支付功能
  - `wechat_pay`: 微信支付配置（app_id, mch_id, key等）
  - `alipay`: 支付宝配置（app_id, private_key, public_key等）

- **上传设置** (`upload`)：
  - `software_package_path`: 软件包存储路径
  - `software_icon_path`: 软件图标存储路径
  - `site_images_path`: 站点图片存储路径
  - `allowed_extensions`: 允许的文件扩展名
  - `max_size`: 最大文件大小限制

### 3. 配置文件加载机制

三个组件（管理后台前端、公共前端、API后端）的配置系统完全独立，提供两套函数分别加载组件配置和站点设置：

1. **前台** (`/wwwroot/common.php`)：
   - `get_config()`：直接返回前台配置文件 (`config.php`) 的内容
   - `get_settings()`：通过 Settings 类加载站点设置文件并返回站点设置

2. **管理后台** (`/wwwroot/admin/common.php`)：
   - `get_config()`：直接返回管理后台配置文件 (`config.php`) 的内容
   - `get_settings()`：加载管理后台配置获取站点设置文件路径，通过 Settings 类加载并返回站点设置

3. **API** (`/wwwroot/api/common.php`)：
   - `get_config()`：直接返回API配置文件 (`config.php`) 的内容
   - `get_settings()`：加载API配置获取站点设置文件路径，通过 Settings 类加载并返回站点设置

所有组件的函数实现类似，但使用各自的根目录常量（`__WWWROOT__`、`__ADMIN_ROOT__`、`__API_ROOT__`）来加载配置文件。

### 4. 配置文件设计原则

1. **命名规范**：
   - 使用 `title` 作为显示标签
   - 使用 `name` 作为标识符
   - 配置文件使用有意义的命名：`config.php`

2. **布尔值存储**：
   - 可以使用数字值（0/1）
   - 当使用 `valueType` => `boolean` 时，可以存储实际布尔值（true/false）

3. **路径处理**：
   - 使用 `dirname` 函数而非 `../` 引用路径
   - 使用 `DS` 常量而非直接使用 `/` 作为路径分隔符
   - 存储路径相对于网站根目录，便于迁移

4. **分组设置**：
   - 站点配置支持多个设置组，按照二级层次结构组织
   - 每个设置组可以在管理界面中显示为单独的标签页
   - 设置组内的选项按照逻辑关系组织

5. **图片管理**：
   - 站点图片（如二维码）存储在专用路径中
   - 支持本地上传功能

## API验证机制

系统实现了两套API验证机制，分别用于管理面板API和公共API：

### 管理面板API验证机制

系统实现了一个API管理面板验证机制，确保所有管理面板API接口的安全性：

1. **验证文件 `panel.php`**：
   - 位于 `/api/admin/panel.php`
   - 所有管理面板API接口（`/api/admin/panel/`目录下的文件）都必须包含此文件
   - 使用统一的包含语句：`require_once dirname(__DIR__) . DS . 'panel.php';`
   - 包含管理员面板验证文件后，会自动加载 `/api/admin/common.php`
   - 验证管理员登录状态，未登录时返回401状态码
   - 检查会话超时（24小时），超时时返回401状态码并提示重新登录
   - 提供权限检查函数：`check_permission()` 和 `require_permission()`
   - 支持单个权限检查或多个权限的"任一"检查模式
   - 支持救援模式，在救援模式下视为超级管理员

2. **权限验证流程**：
   - API接口首先包含 `panel.php` 文件
   - 验证管理员登录状态和会话有效性
   - 使用 `require_permission()` 函数检查特定操作的权限
   - 权限不足时返回403状态码
   - 所有API接口都必须经过权限验证才能执行操作

3. **统一错误处理**：
   - 未登录：返回401状态码和JSON响应 `{'success': false, 'message': '未登录或会话已过期', 'code': 'unauthorized'}`
   - 会话超时：返回401状态码和JSON响应 `{'success': false, 'message': '会话已过期，请重新登录', 'code': 'session_expired'}`
   - 权限不足：返回403状态码和JSON响应 `{'success': false, 'message': '没有执行此操作的权限', 'code': 'permission_denied'}`

### 公共API验证机制

系统实现了一个公共API验证机制，确保所有公共API接口的一致性和安全性：

1. **验证文件 `public.php`**：
   - 位于 `/api/public.php`
   - 所有公共API接口（`/api/public/`目录下的文件）都必须包含此文件
   - 使用统一的包含语句：`require_once dirname(__DIR__) . DS . 'public.php';`
   - 包含公共API验证文件后，会自动加载 `/api/common.php`
   - 检查系统是否已安装，未安装时自动重定向或返回JSON响应
   - 提供下载令牌生成和验证函数：`generate_download_token()` 和 `validate_download_token()`
   - 提供下载日志记录函数：`log_download()`
   - 支持下载密钥管理：`get_download_secret()`

2. **下载令牌验证流程**：
   - 生成下载令牌时，使用软件ID、时间戳和订单号（可选）生成签名
   - 验证下载令牌时，检查时间戳是否在有效期内（默认5分钟）
   - 验证签名是否匹配，防止令牌被篡改
   - 验证成功后，返回软件ID和订单号，用于后续处理

3. **统一错误处理**：
   - 系统未安装：返回303状态码和安装页面URL
   - 令牌无效：返回403状态码和JSON响应 `{'success': false, 'message': '下载令牌无效'}`
   - 令牌过期：返回403状态码和JSON响应 `{'success': false, 'message': '下载链接已过期，请刷新页面重试'}`

## API接口设计

### 前台公共API

| 端点 | 方法 | 说明 | 参数 |
|------|------|------|------|
| `/api/public/install.php` | POST | 系统安装 | username, password, siteTitle, siteSubtitle, dbPath |
| `/api/public/index.php` | GET | 获取软件列表和分类 | category, search, page, pageSize, flag, software_id |
| `/api/public/download.php` | GET | 软件下载 | id, token, order_no |
| `/api/public/payment.php` | GET/POST | 支付处理 | software_id, payment_type, order_no |
| `/api/public/payment_notify.php` | POST | 支付回调通知 | 支付平台回调参数 |
| `/api/public/payment_return.php` | GET | 支付结果API | action, order_no |
| `/payment_return.php` | GET | 支付结果页面 | order_no |

**API参数说明**：
- `/api/public/index.php` 新增 `software_id` 参数：
  - 当提供 `software_id` 参数时，返回单个软件的详细信息
  - 用于支持URL路由功能，通过软件ID直接获取软件信息
  - 返回格式：`{'success': true, 'software': {...}}`

- `/api/public/payment_return.php` 新增action参数：
  - `action=get_order_info&order_no={订单号}`：获取订单详细信息
  - `action=check_order&order_no={订单号}`：检查订单状态
  - `action=verify_alipay_return`：验证支付宝同步回调
  - 用于支持支付宝回调处理和订单状态查询

### 后台认证API

| 端点 | 方法 | 说明 | 参数 |
|------|------|------|------|
| `/api/admin/public/login.php` | POST | 管理员登录 | username, password |
| `/api/admin/public/logout.php` | GET | 管理员登出 | - |

### 后台管理API

| 端点 | 方法 | 说明 | 参数 |
|------|------|------|------|
| `/api/admin/panel/software.php` | GET/POST | 软件管理 | 根据操作不同 |
| `/api/admin/panel/category.php` | GET/POST | 分类管理 | 根据操作不同 |
| `/api/admin/panel/admin.php` | GET/POST | 管理员管理 | 根据操作不同 |
| `/api/admin/panel/roles.php` | GET/POST | 角色管理 | 根据操作不同 |
| `/api/admin/panel/permissions.php` | GET | 权限信息和检查 | action, permission |
| `/api/admin/panel/settings.php` | GET/POST | 站点设置管理 | 各种站点设置参数 |
| `/api/admin/panel/software_upload.php` | POST | 软件和图标上传 | type, software_id, file |
| `/api/admin/panel/settings_upload.php` | POST | 站点设置相关图片上传 | field_name, type, sub_path, file |
| `/api/admin/panel/payment_notify.php` | GET/POST/DELETE | 支付通知记录管理 | 根据操作不同 |

## 前端技术栈

系统前端采用以下技术栈：

- **HTML5**：页面结构
- **CSS3**：样式设计，使用Tailwind CSS框架
- **JavaScript**：客户端逻辑
- **Fetch API**：网络请求
- **Font Awesome**：图标库

样式采用Tailwind CSS，提供现代化的UI组件和响应式设计。

### 浏览器兼容性优化

系统针对360浏览器等双核浏览器进行了兼容性优化：

1. **360浏览器极速模式强制**：
   - 在所有页面的HTML头部添加了 `<meta name="renderer" content="webkit">` 标签
   - 强制360浏览器使用极速模式（Webkit内核）而非兼容模式（IE内核）
   - 提供更好的现代Web标准支持和用户体验

2. **IE兼容性设置**：
   - 添加了 `<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">` 标签
   - 确保IE浏览器使用最新的渲染引擎
   - 优先使用Chrome Frame插件（如果已安装）

3. **IE8 专用兼容版本**：
   - 创建了 `index-ie8.php` 文件，专门为 IE8 浏览器提供完全兼容的界面
   - **技术特点**：
     - 移除了 Tailwind.js 和 Vue.js 依赖，使用纯 CSS 和原生 JavaScript
     - 使用 IE8 支持的 CSS 属性和语法（避免 flexbox、grid、transform 等）
     - 使用 IE8 兼容的渐变语法和滤镜效果
     - 实现了完整的 AJAX 功能，兼容 IE8 的 XMLHttpRequest
     - 使用 float 布局替代现代布局方案
   - **视觉效果**：
     - 保持与现代版本完全相同的科技风格设计
     - 使用 IE8 的 `filter` 属性实现发光和阴影效果
     - 保持相同的颜色方案、渐变背景和动画效果
     - 响应式布局适配移动设备
   - **功能完整性**：
     - 软件列表显示和分类筛选
     - 搜索功能（支持即时搜索和点击搜索）
     - 分页功能
     - 下载选项弹窗
     - 公告系统
     - 二维码弹窗
     - 支付功能基础框架
   - **兼容性处理**：
     - 使用 `<!--[if lt IE 9]>` 条件注释加载 HTML5 和 CSS3 兼容库
     - 实现了完整的 DOM 操作兼容函数（addClass、removeClass、hasClass 等）
     - 事件绑定兼容 IE8 的 attachEvent 方法
     - localStorage 降级到 cookie 存储

3. **覆盖范围**：
   - 前台首页（`/index.php`）
   - 管理后台登录页面（`/admin/public/login.php`）
   - 管理后台所有页面（通过 `/admin/panel.php` 的 `render_header` 函数）
   - 支付结果页面（`/payment_return.php`）
   - 系统安装页面（`/install.php`）

4. **JavaScript弹窗兼容性优化**：
   - 支付选择弹窗和公告弹窗使用内联样式替代Tailwind CSS类名
   - 避免使用较旧Chrome内核不支持的CSS属性（如 `bg-opacity-70`、`inset-0`）
   - 使用Flexbox替代CSS Grid确保更好的兼容性
   - 通过JavaScript事件监听器实现hover效果，替代CSS伪类
   - 确保在360浏览器极速模式（较旧Chrome内核）中正常显示弹窗

5. **弹窗界面一致性**：
   - 支付选择弹窗采用与下载选项弹窗相同的科技风格设计
   - 统一使用深蓝色渐变背景 `linear-gradient(135deg, #001133, #003366)`
   - 统一的边框和发光效果 `border: 1px solid #00aaff; box-shadow: 0 0 30px rgba(0, 170, 255, 0.5)`
   - 统一的脉冲动画效果 `animation: pulse 2s infinite`
   - 统一的颜色方案：青色标题 `#00ffff`、蓝色文字 `#aaccff`、黄色价格 `#fbbf24`
   - 统一的选项卡片样式：半透明蓝色背景、圆形图标、左对齐文本布局
   - 统一的hover效果：背景色变化和边框高亮

6. **支付流程优化**：
   - 微信支付二维码界面在同一弹窗中显示，避免多个弹窗层叠
   - 支付选择 → 二维码扫描 → 返回选择，全程在同一弹窗容器中切换内容
   - 二维码界面提供"返回"和"取消"两个操作选项
   - 通过自定义事件 `payment:showOptions` 实现界面间的通信
   - 保持支付状态检查和自动跳转功能的完整性
   - 支付宝仍使用跳转方式，微信支付使用弹窗内容替换方式

7. **通用弹窗系统**：
   - 实现了统一的弹窗管理器 `UniversalModal` 类（`/assets/js/modal.js`）
   - 所有弹窗（下载选项、支付选择、支付二维码、公告）使用同一个HTML容器 `#universalModal`
   - 避免了重复创建DOM元素和多个弹窗同时存在的问题
   - 统一的样式管理：所有弹窗都使用相同的科技风格设计
   - 统一的事件处理：关闭、取消、hover效果等都由弹窗管理器统一处理
   - 支持弹窗类型：`download`、`payment-options`、`payment-qr`、`announcement`
   - 弹窗内容动态生成，根据类型和数据自动构建HTML结构
   - 平滑的显示/隐藏动画效果，提升用户体验
   - 完全替代了原有的 `insertAdjacentHTML` 方式，提高了代码的可维护性

8. **支付弹窗样式重构**：
   - 支付选择弹窗采用现代化设计，兼容360浏览器早期Chrome内核
   - 使用深色主题：`#111827` 背景色，圆角设计，阴影效果
   - 微信支付按钮：`#059669` 绿色背景，hover时变为 `#047857`，内边距 `1rem 2rem`
   - 支付宝按钮：`#2563eb` 蓝色背景，hover时变为 `#1d4ed8`，内边距 `1rem 2rem`
   - 支持配置化的按钮布局：通过 `payment.button_layout` 设置
   - 水平布局（`horizontal`）：使用Flexbox布局，按钮平分宽度，间距合理
   - 垂直布局（`vertical`）：使用Flexbox纵向布局，按钮上下排列
   - 兼容性优化：避免使用CSS Grid，全部使用Flexbox确保早期浏览器兼容
   - 内联样式：所有样式使用内联方式，避免外部CSS类名兼容性问题
   - 简洁的信息展示：软件名称、价格、说明文字清晰分层
   - 平滑的过渡动画：`transition: background-color 0.2s ease`
   - JavaScript hover效果：通过事件监听器实现hover效果，确保兼容性
   - 与其他弹窗（下载选项、公告等）保持科技风格的区别

9. **发光框对齐优化**：
   - 支付弹窗拥有独立的发光效果，与弹窗尺寸和圆角完美贴合
   - 专用动画：`paymentPulse` 3秒循环，发光强度从30px到40px渐变
   - 多层阴影效果：结合常规阴影和发光阴影，层次丰富
   - 边框发光：`border: 1px solid rgba(0, 170, 255, 0.3)` 配合动画变化
   - 自适应容器：通用弹窗容器样式透明化，让支付弹窗自定义样式生效
   - 尺寸匹配：发光框大小与弹窗内容（700px最大宽度，8px圆角）完全一致
   - 动画同步：发光强度和边框颜色同步变化，视觉效果统一
   - 兼容性保证：使用标准CSS动画和box-shadow，确保早期浏览器支持

10. **支付宝跳转动画**：
    - 点击支付宝按钮时显示加载动画，提升用户体验
    - 按钮状态变化：禁用按钮，降低透明度，改变鼠标样式
    - 加载图标：使用 `fas fa-spinner fa-spin` 旋转图标
    - 文字提示：显示"正在跳转..."提示用户当前状态
    - 脉冲动画：`alipayPulse` 1秒循环，按钮轻微缩放和颜色变化
    - 延迟跳转：800ms延迟让用户看到完整的加载动画
    - 视觉反馈：按钮从 `#2563eb` 到 `#1d4ed8` 颜色渐变
    - 缩放效果：按钮在动画中轻微放大（scale 1.02）增强视觉效果

### 前端资源本地化

为了提高加载速度和减少对外部CDN的依赖，系统将所有前端资源本地化存储：

1. **本地资源目录结构**：
   ```
   /assets/
   ├── libs/                   # 第三方库目录
   │   ├── vue/               # Vue.js
   │   ├── axios/             # Axios HTTP客户端
   │   ├── chart.js/          # Chart.js图表库
   │   ├── md5/               # MD5加密库
   │   ├── qrcode/            # 二维码生成库
   │   ├── tailwindcss/       # Tailwind CSS
   │   └── font-awesome/      # Font Awesome图标
   ```

2. **CDN资源映射**：
   - Vue.js 2.x：`/assets/libs/vue/vue.min.js`
   - Axios：`/assets/libs/axios/axios.min.js`
   - Chart.js：`/assets/libs/chart.js/chart.min.js`
   - MD5库：`/assets/libs/md5/md5.min.js`
   - QRCode.js：`/assets/libs/qrcode/qrcode.min.js`
   - Tailwind CSS：`/assets/libs/tailwindcss/tailwind.min.css`
   - Font Awesome：`/assets/libs/font-awesome/css/all.min.css`

3. **本地化优势**：
   - 提高页面加载速度
   - 减少对外部CDN的依赖
   - 提高系统稳定性和可靠性
   - 支持离线环境部署

## 管理后台前端设计

管理后台前端采用特殊设计，确保生成静态HTML并支持可配置路径：

### 1. 静态HTML生成

- 管理后台前端（`/wwwroot/admin/`）可以加载站点设置数据并生成HTML
- 不执行HTML本身不支持的动作，如操作header、读写会话或服务器端重定向
- 不使用PHP会话（`$_SESSION`）或服务器端重定向（`header()`）
- 所有动态交互功能通过客户端JavaScript实现
- 登录状态和权限检查在客户端完成
- 静态资源通过 `$adminConfig['assets_path']` 引用，而不是使用 `$basePath`（用于页面URL）

### 2. 单一入口系统

- 所有页面通过单一入口点访问：`/index.php/public/login.html`
- 路径格式为：`/index.php/[area]/[page].html`
- 入口路径可在`/wwwroot/admin/config.php`中配置

### 3. 客户端认证

- 用户凭据存储在浏览器的`localStorage`中，而非服务器会话
- 登录状态检查通过JavaScript完成
- 会话超时在客户端计算和处理
- 权限信息存储在`localStorage`中并在客户端验证

### 4. 统一UI模块系统

为了减少代码重复和提高维护效率，系统实现了统一的UI模块系统：

#### 4.1 Vue.js 基础组件

**AdminListView 组件** (`/assets/js/components/AdminListView.js`)：
- 提供标准的列表页面结构和功能
- 包含搜索、筛选、分页、CRUD操作的通用逻辑
- 支持自定义配置和扩展

**AdminModal 组件** (`/assets/js/components/AdminModal.js`)：
- 统一的模态框组件
- 支持添加、编辑、删除确认等不同类型
- 自动处理表单验证和提交

**AdminTable 组件** (`/assets/js/components/AdminTable.js`)：
- 标准化的数据表格组件
- 支持排序、筛选、操作按钮
- 响应式设计和加载状态

#### 4.2 CSS 样式系统

**统一样式文件** (`/assets/css/admin-common.css`)：
- 包含所有Tailwind CSS类名的定义
- 统一的颜色方案和间距规范
- 响应式断点和动画效果
- 深色主题的完整实现

**组件样式** (`/assets/css/admin-components.css`)：
- 各个组件的专用样式
- 模态框、表格、按钮等组件样式
- 状态指示器和图标样式

#### 4.3 JavaScript 工具库

**AdminUtils** (`/assets/js/utils/AdminUtils.js`)：
- 通用工具函数集合
- 日期格式化、权限检查、数据验证等
- 防抖搜索、分页计算等功能

**FormValidator** (`/assets/js/utils/FormValidator.js`)：
- 统一的表单验证系统
- 支持各种验证规则
- 错误提示和样式处理

#### 4.4 页面模板系统

**页面基础模板** (`/admin/templates/`)：
- `list-page.template.php`: 列表页面模板
- `form-page.template.php`: 表单页面模板
- `detail-page.template.php`: 详情页面模板

**使用方式**：
```php
// 在新页面中使用模板
$pageConfig = [
    'title' => '支付通知记录',
    'apiEndpoint' => 'payment_notify',
    'permissions' => ['payment_notify.view'],
    'columns' => [...],
    'filters' => [...],
    'actions' => [...]
];
include_template('list-page', $pageConfig);
```

#### 4.5 配置驱动的页面生成

**页面配置文件** (`/admin/configs/pages/`)：
- 每个页面对应一个配置文件
- 定义字段、权限、操作等
- 支持继承和覆盖

**自动生成器**：
- 根据配置自动生成页面代码
- 减少重复代码编写
- 保证UI一致性

#### 4.6 实现效果

通过统一UI模块系统，实现了以下效果：

**代码复用率提升**：
- 新页面开发时间减少80%以上
- 重复代码减少90%以上
- 维护成本大幅降低

**UI一致性保证**：
- 所有页面使用相同的设计语言
- 统一的交互模式和用户体验
- 自动应用主题和样式更新

**开发效率提升**：
- 配置驱动的页面生成
- 标准化的组件和工具函数
- 简化的开发流程

**示例对比**：

传统方式（支付通知记录页面）：
- 代码行数：~500行
- 开发时间：2-3小时
- 重复代码：~80%

使用统一模块系统：
- 配置文件：~120行
- 页面文件：~200行
- 开发时间：30分钟
- 重复代码：~10%

**使用示例**：
```php
// 页面配置文件 /admin/configs/pages/example.config.php
return [
    'title' => '示例管理',
    'apiEndpoint' => '/api/admin/panel/example.php',
    'permissions' => ['example.view'],
    'columns' => [...],
    'filters' => [...],
    'actions' => [...]
];

// 页面文件 /admin/panel/example.php
require_once dirname(__DIR__) . '/panel.php';
$pageConfig = include dirname(__DIR__) . '/configs/pages/example.config.php';
include_template('list-page', $pageConfig);
```

### 5. 文件上传功能

管理后台提供文件上传功能，支持以下特性：

- 软件文件上传：支持ZIP、RAR、7Z、EXE、MSI等格式
- 图标上传：支持JPEG、PNG、GIF、WEBP等格式
- 站点图片上传：支持JPEG、PNG、GIF、WEBP等格式，用于上传二维码等站点图片
- 自动保存到数据库：编辑软件时上传文件会自动更新数据库
- 文件预览：上传图标或站点图片后可以预览图片
- 图片刷新：提供刷新按钮，解决图片缓存问题
- 文件大小自动填充：上传软件文件后自动填充软件大小字段
- 前台URL预览：通过配置前台网站URL，实现图片上传后的预览功能
- 图片上传错误处理：自动处理图片加载错误，尝试使用不同路径加载
- 本地上传/下载功能：支持软件和图标的本地上传和下载
- 文件命名规则：使用软件ID作为文件名；对于新条目，先创建数据库记录再使用新ID作为文件名
- 站点图片命名：对于站点图片（如二维码），使用固定文件名而非随机名称
- 数据库链接自动保存：上传过程中自动保存数据库链接，防止冗余文件

### 5. 订单管理功能

管理后台提供订单管理功能，支持以下特性：

- **订单列表界面**：
  - 显示所有订单的基本信息：订单号、软件名称、金额、支付方式、状态等
  - 支持按订单号和软件名称搜索
  - 支持按支付状态筛选（待支付、已支付、支付失败）
  - 提供编辑和删除操作入口
  - 分页显示订单列表

- **订单状态管理**：
  - 支持手动更改订单状态（待支付、已支付、支付失败）
  - 当订单状态改为已支付时，自动添加下载权限
  - 显示订单创建时间和支付时间

- **订单删除功能**：
  - 支持删除订单记录
  - 删除前需要确认
  - 删除操作不可撤销

### 6. 支付设置

管理后台提供支付设置功能，支持以下支付方式：

- 微信支付：配置AppID、商户ID、API密钥等
- 支付宝：配置AppID、私钥、公钥等
- 支付方式开关：可以启用或禁用特定的支付方式

## 认证系统

系统提供两种认证方式：

### 1. 数据库认证
- 从数据库表`admins`验证用户名和密码
- 密码采用盐值+哈希存储，增强安全性
- 登录成功后，将用户信息存储在会话中

### 2. 救援模式认证
- 当数据库认证不可用时的备选方案
- 管理员凭据（用户名和密码）存储在`site_settings.php`中
- 认证流程：
  - 检查site_settings.php中是否存在非空的救援密码
  - 如果用户输入的凭据与配置匹配，则允许登录
  - 登录成功后，会自动将该用户添加到数据库（如果不存在）
  - 登录后清除配置中的明文密码，只保留用户名，密码设为空字符串

## 权限控制系统

系统采用基于角色的访问控制（RBAC）：

### 角色和权限
- 每个管理员都有一个关联的角色（存储在`role_id`字段）
- 角色定义了一组权限，存储为JSON格式
- 系统预设三种角色：
  1. **超级管理员**: 拥有所有权限(`all`)
  2. **内容管理员**: 可以管理软件和分类
  3. **查看员**: 只有查看权限

### 权限列表
系统定义了以下权限：

- **管理员管理**
  - `admin.view`: 查看管理员列表
  - `admin.add`: 添加管理员
  - `admin.edit`: 编辑管理员
  - `admin.delete`: 删除管理员

- **角色管理**
  - `role.view`: 查看角色列表
  - `role.add`: 添加角色
  - `role.edit`: 编辑角色
  - `role.delete`: 删除角色

- **软件管理**
  - `software.view`: 查看软件列表
  - `software.add`: 添加软件
  - `software.edit`: 编辑软件
  - `software.delete`: 删除软件

- **分类管理**
  - `category.view`: 查看分类列表
  - `category.add`: 添加分类
  - `category.edit`: 编辑分类
  - `category.delete`: 删除分类
  - **删除限制**：分类删除功能包含智能限制检查，只有当分类下没有软件且没有子分类时才能删除
  - **技术实现**：使用正则表达式 `/\b([a-zA-Z_][a-zA-Z0-9_]*)\b/g` 替换条件字符串中的变量名，避免数字被错误替换导致的语法错误

- **订单管理**
  - `orders.view`: 查看订单列表
  - `orders.edit`: 编辑订单状态
  - `orders.delete`: 删除订单

- **支付通知记录**
  - `payment_notify.view`: 查看支付通知记录
  - `payment_notify.verify`: 手动验签支付通知

- **数据统计**
  - `stats.view`: 查看系统统计

- **系统**
  - `all`: 所有权限（超级管理员）

### 权限检查
- 在每个API端点执行操作前都会检查相应权限
- 使用`check_permission()`函数检查权限
- 使用`require_permission()`函数强制要求权限
- 前端页面使用`check_panel_permission()`检查访问权限

## 角色管理功能

系统提供完整的角色管理功能：

1. **角色列表界面**：
   - 显示所有角色的基本信息：名称、描述、创建时间
   - 提供编辑和删除操作入口
   - 超级管理员角色不可删除

2. **角色添加/编辑**：
   - 支持设置角色名称和描述
   - 提供权限分组设置界面，按功能模块分类
   - 支持"全部权限"选项（超级管理员）

3. **权限分配**：
   - 采用复选框方式分组显示权限
   - 权限按功能模块分类：管理员管理、角色管理、软件管理、分类管理、统计管理
   - 提供直观的界面设置各模块的增删改查权限

4. **权限存储**：
   - 权限以JSON格式存储在数据库中
   - 支持两种格式：字符串"all"（表示所有权限）或权限ID数组

## 管理员管理增强功能

增强了管理员管理功能：

1. **角色分配**：
   - 创建管理员时可指定角色
   - 编辑管理员时可更改角色
   - 不能降低自己的权限（防止锁定超级管理员）

2. **密码重置**：
   - 管理员可重置其他管理员的密码
   - 系统自动生成随机密码并显示
   - 重置过程不需要知道原密码

3. **密码修改**：
   - 管理员可修改自己的密码
   - 需要输入当前密码进行验证
   - 提供密码确认机制避免输入错误

4. **删除保护**：
   - 不能删除自己的账号
   - 不能删除最后一个管理员账号
   - 删除操作需要二次确认

## 特殊功能：救援模式

系统支持"救援模式"，为管理员提供紧急访问机制：

### 特点
1. 管理员凭据存储在`site_settings.php`中作为明文
2. 当数据库访问不可用时，仍可以使用这些凭据登录
3. 登录成功后，会自动执行以下操作：
   - 创建或更新管理员账户（如果数据库可访问）
   - 清除配置文件中的明文密码（设置为空字符串）
   - 在会话中标记为救援模式登录

### 触发条件
- 只要在site_settings.php中设置了非空密码，系统就会启用救援模式
- 无需显式标记或切换救援模式

## API端点

系统提供以下主要API端点：

### 管理员API
- `/api/admin/public/login.php`: 管理员登录
- `/api/admin/panel/admin.php`: 管理员管理（CRUD操作）
- `/api/admin/panel/roles.php`: 角色管理（CRUD操作）
- `/api/admin/panel/permissions.php`: 权限信息和检查

### 内容管理API
- `/api/admin/panel/software.php`: 软件管理（CRUD操作）
- `/api/admin/panel/category.php`: 分类管理（CRUD操作）
- `/api/admin/panel/bulk-update.php`: 批量更新操作
- `/api/admin/panel/orders.php`: 订单管理（CRUD操作）

### 公共API
- `/api/public/index.php`: 获取软件列表和分类（使用flag参数可在初始加载时同时返回两者）
- `/api/public/download.php`: 处理软件下载请求
- `/api/public/payment.php`: 处理支付请求
- `/api/public/payment_notify.php`: 处理支付平台回调通知
- 公告信息直接从站点设置文件(`site_settings.php`)中获取，无需单独API

### 统计API
- `/api/admin/panel/stats.php`: 系统统计数据

## 支付系统

系统实现了完整的支付功能，支持微信支付和支付宝，并包含支付通知记录和调试功能：

### 支付签名验证系统

系统实现了独立的支付签名验证类 `PaymentSignature`，提供统一的签名生成和验证功能：

1. **PaymentSignature 类**（位于 `/api/includes/PaymentSignature.php`）：
   - **微信支付签名**：
     - `generateWechatSign()`: 生成微信支付签名
     - `verifyWechatSign()`: 验证微信支付签名
   - **支付宝签名**：
     - `generateAlipaySign()`: 生成支付宝RSA2签名
     - `verifyAlipaySign()`: 验证支付宝RSA2签名
     - `formatPrivateKey()`: 格式化私钥
     - `formatPublicKey()`: 格式化公钥
   - **测试功能**：
     - `testAlipayConfig()`: 测试支付宝配置
     - `testAlipaySign()`: 测试支付宝签名

2. **验签算法特点**：
   - 支持参数预处理和排序
   - 自动处理PEM格式密钥
   - 提供详细的错误信息
   - 支持调试模式跳过验证

### 支付通知记录系统

系统实现了完整的支付通知记录功能，用于开发调试和问题排查：

1. **数据库表结构**：
   - `payment_notify_logs` 表记录所有支付通知
   - 包含原始数据、解析数据、签名验证结果、处理结果等信息
   - 支持按支付类型、验证状态、处理结果等条件筛选

2. **自动记录功能**：
   - 所有进入 `payment_notify.php` 的请求都会自动记录
   - 记录包括微信支付和支付宝的通知
   - 自动执行签名验证并记录结果
   - 记录处理成功或失败的详细信息

3. **管理后台功能**：
   - **支付通知记录页面**（`/admin/panel/payment-notify.html`）：
     - 支持多条件筛选：支付类型、签名验证状态、处理结果、订单号、IP地址等
     - 分页显示记录列表
     - 显示验证失败记录的数量徽章
   - **详情查看功能**：
     - 查看完整的通知数据（原始数据和解析数据）
     - 显示签名验证和处理结果详情
     - 支持查看错误信息和调试信息
   - **手动验签功能**：
     - 对验证失败或未验证的记录进行手动验签
     - 使用当前的支付配置重新验证签名
     - 更新验证结果到数据库

4. **权限控制**：
   - `payment_notify.view`: 查看支付通知记录权限
   - `payment_notify.verify`: 手动验签权限
   - 集成到角色权限管理系统

5. **API接口**：
   - `/api/admin/panel/payment_notify.php`: 支付通知记录管理API
   - 支持GET（列表查询、详情查看）、POST（手动验签）、DELETE（删除记录）操作
   - 提供完整的筛选、分页和搜索功能

### 1. 支付流程

1. **创建订单**：
   - 用户点击下载付费软件时，系统检查是否需要支付
   - 如果需要支付，显示支付选择弹窗
   - 用户选择支付方式后，系统创建订单

2. **支付处理**：
   - **微信支付**：
     - 移动设备：跳转到微信H5支付页面
     - 桌面设备：显示二维码，用户使用微信扫码支付
   - **支付宝**：
     - 移动设备：跳转到支付宝手机网页支付
     - 桌面设备：直接跳转到支付宝网页支付页面

3. **支付状态检查**：
   - **微信支付桌面端**：系统定期检查支付状态，支付成功后跳转到软件页面显示下载选项弹窗
   - **支付宝**：用户完成支付后自动跳转到支付结果页面 (`/payment_return.php`)
   - 支付失败时显示错误信息

4. **支付结果处理**：
   - **微信支付**：通过轮询检测支付状态，成功后触发 `payment:success` 事件并跳转到软件页面显示下载选项弹窗
     - 桌面端显示二维码：使用 QRCode.js 库在 Canvas 元素中生成二维码
     - 二维码生成：创建 Canvas 元素并使用 `QRCode.toCanvas` 方法生成二维码图像
   - **支付宝**：跳转到专门的支付结果页面，该页面会：
     - 自动检查支付状态（每3秒检查一次）
     - 支付成功后显示倒计时（5秒）
     - 自动跳转到软件页面显示下载选项弹窗
     - 提供手动重新下载和返回首页的选项

5. **支付完成后的处理机制**：
   - **微信支付**：支付成功后触发 `payment:success` 事件，延迟1秒后跳转到软件页面（`/#/software/{id}`）显示下载选项弹窗
   - **支付宝**：支付成功后跳转到支付结果页面，验证支付状态后跳转到软件页面（`/#/software/{id}`）显示下载选项弹窗
   - 前端监听支付成功事件，可执行额外的处理逻辑
   - 两种支付方式最终都会跳转到软件页面，保持用户体验的一致性

### 2. 支付方式

1. **微信支付**：
   - 支持扫码支付（PC端）
   - 支持H5支付（移动端）
   - 配置项：AppID、商户ID、API密钥
   - 默认回调地址：`api/public/payment_notify.php?type=wechat_pay`

2. **支付宝**：
   - 支持网页支付（PC端和移动端统一跳转）
   - 配置项：AppID、私钥、公钥
   - 支付完成后通过 `return_url` 参数自动跳转到支付结果页面
   - 默认回调地址：`api/public/payment_notify.php?type=alipay`

### 3. 支付结果页面

系统提供专门的支付结果页面 (`/payment_return.php`) 用于处理支付宝支付完成后的跳转：

**架构说明**：
- 前端页面：`/payment_return.php`（纯HTML页面，用户友好的URL）
- 后端API：`/api/public/payment_return.php`（纯后端处理，返回JSON数据）
- 前后端分离：前端通过JavaScript调用后端API获取数据

1. **页面功能**：
   - **智能回调处理**：自动检测是否为支付宝同步回调
   - **支付宝回调**：验证回调参数签名，自动完成订单，无需轮询
   - **普通访问**：显示订单信息，定期检查支付状态（每3秒检查一次）
   - **自动下载**：支付成功后自动下载软件

2. **用户体验**：
   - 现代化的UI设计，使用毛玻璃效果
   - 实时状态更新，包括检查中、成功、失败状态
   - 支付成功后5秒倒计时自动下载
   - 提供手动重新下载和返回首页的选项

3. **技术实现**：
   - **回调检测**：JavaScript检测URL中是否包含支付宝回调参数（sign、out_trade_no、total_amount）
   - **签名验证**：后端使用支付宝公钥验证回调参数的RSA2签名
   - **调试模式**：支持在后台设置中启用跳过签名验证功能（仅用于测试）
   - **订单完成**：验证成功后自动更新订单状态为已支付
   - **状态轮询**：非回调访问时使用JavaScript定期调用支付状态API
   - **自动下载**：自动生成下载令牌并构建下载链接
   - **响应式设计**：适配移动端和桌面端

### 4. 调试设置

系统提供了调试功能来帮助开发和测试：

1. **调试模式开关**：
   - 路径：后台设置 → 调试设置 → 启用调试模式
   - 启用后显示详细的错误信息和调试日志

2. **跳过支付签名验证**：
   - 路径：后台设置 → 调试设置 → 跳过支付签名验证
   - ⚠️ **仅用于测试环境**：跳过支付宝/微信支付的签名验证
   - 生产环境请务必关闭此功能

3. **支付回调日志**：
   - 路径：后台设置 → 调试设置 → 记录支付回调日志
   - 记录所有支付回调的详细信息到日志文件

### 5. 支付配置说明

系统为支付回调URL提供了默认配置，简化了配置过程：

1. **默认回调地址**：
   - 微信支付：`api/public/payment_notify.php?type=wechat_pay`
   - 支付宝：`api/public/payment_notify.php?type=alipay`

2. **URL处理机制**：
   - 系统会自动将相对路径的回调地址转换为完整URL
   - 使用站点设置中的 `frontend_url` 作为基础URL
   - 例如：`https://yourdomain.com/api/public/payment_notify.php?type=alipay`

3. **配置优势**：
   - 无需手动填写完整的回调URL
   - 支持站点迁移，自动适应新域名
   - 减少配置错误的可能性

### 6. 支付宝签名验证详解

系统实现了完整的支付宝RSA2签名验证机制，确保支付安全性：

#### 6.1 验签相关文件

- **签名生成**：`/api/payment.php` - `generate_alipay_sign()` 函数
- **异步回调验签**：`/api/public/payment_notify.php` - `process_alipay_notify()` 函数
- **同步回调验签**：`/api/public/payment_return.php` - `verify_alipay_return` 操作
- **测试验签**：`/api/public/payment_return.php` - `test_sign` 操作

#### 6.2 验签算法流程

1. **参数预处理**：
   ```php
   // 移除不参与签名的参数
   unset($params['sign'], $params['sign_type']);
   // 移除空值参数
   foreach ($params as $k => $v) {
       if ($v === '' || is_null($v)) {
           unset($params[$k]);
       }
   }
   ```

2. **参数排序**：
   ```php
   // 按参数名ASCII码从小到大排序
   ksort($params);
   ```

3. **构建待签名字符串**：
   ```php
   $stringToBeSigned = '';
   foreach ($params as $k => $v) {
       $stringToBeSigned .= $k . '=' . $v . '&';
   }
   $stringToBeSigned = rtrim($stringToBeSigned, '&');
   ```

4. **公钥格式化**：
   ```php
   $publicKey = "-----BEGIN PUBLIC KEY-----\n" .
                wordwrap($config['public_key'], 64, "\n", true) .
                "\n-----END PUBLIC KEY-----";
   ```

5. **RSA2签名验证**：
   ```php
   $result = openssl_verify($stringToBeSigned, base64_decode($sign), $publicKey, OPENSSL_ALGO_SHA256);
   // $result === 1 表示验证成功
   ```

#### 6.3 关键验证点

1. **必要参数检查**：
   - `sign`：签名值
   - `out_trade_no`：商户订单号
   - `total_amount`：交易金额

2. **业务参数验证**：
   - 订单号匹配检查
   - 金额一致性验证
   - 交易状态确认（`TRADE_SUCCESS` 或 `TRADE_FINISHED`）

3. **安全措施**：
   - 公钥格式验证
   - 签名算法固定为RSA2（SHA256）
   - 支持调试模式跳过验证（仅测试环境）

#### 6.4 调试功能

系统提供多个调试接口帮助排查验签问题：

1. **测试配置**：`/api/public/payment_return.php?action=test_config`
   - 检查支付宝配置完整性
   - 验证公钥格式正确性
   - 返回配置状态信息

2. **测试签名**：`/api/public/payment_return.php?action=test_sign`
   - 使用固定测试参数验证签名
   - 返回详细的验签过程信息
   - 包含待签名字符串和验证结果

3. **调试模式**：
   - 路径：后台设置 → 调试设置 → 跳过支付签名验证
   - 启用后跳过签名验证，直接返回成功
   - ⚠️ 仅用于测试环境，生产环境必须关闭

#### 6.5 常见问题排查

1. **签名验证失败**：
   - 检查支付宝公钥是否正确
   - 确认参数编码为UTF-8
   - 验证参数值是否被意外修改

2. **公钥格式错误**：
   - 确保公钥包含完整的PEM格式头尾
   - 检查公钥内容是否有换行或空格问题

3. **参数缺失**：
   - 确认回调URL中包含所有必要参数
   - 检查参数名称是否正确

4. **时间同步问题**：
   - 确保服务器时间与支付宝服务器时间同步
   - 检查时间戳格式是否正确

## 文件管理系统

系统实现了完整的文件管理功能：

### 1. 文件上传

- 支持软件文件上传：ZIP、RAR、7Z、EXE、MSI等格式
- 支持图标上传：JPEG、PNG、GIF、WEBP等格式
- 支持站点图片上传：JPEG、PNG、GIF、WEBP等格式
- 文件自动重命名，防止文件名冲突
- 自动保存到数据库，防止忘记保存导致的重复上传

### 2. 上传处理分离

- **软件上传处理**：`/api/admin/panel/software_upload.php`
  - 专门处理软件和图标的上传
  - 支持软件ID关联，自动更新数据库记录
  - 对于新软件，先创建数据库记录，再使用新ID作为文件名
  - 返回软件ID、文件路径、文件大小等信息

- **设置上传处理**：`/api/admin/panel/settings_upload.php`
  - 专门处理站点设置相关的图片上传（如二维码等）
  - 支持指定子路径和文件名
  - 使用固定文件名或随机文件名
  - 返回文件URL、文件名、文件大小等信息

### 3. 文件存储

- 统一的存储路径配置：
  - `upload.software_package_path` - 软件包文件存储路径 (默认: uploads/software/package/)
  - `upload.software_icon_path` - 软件图标存储路径 (默认: uploads/software/icon/)
  - `upload.site_images_path` - 站点图片存储路径 (默认: uploads/site/)
- 自动创建存储目录，确保路径存在
- 文件安全性检查，防止上传恶意文件
- 文件大小限制，防止上传过大文件
- 站点图片（如二维码）使用固定文件名，便于管理和引用

### 4. 文件下载

- 支持本地文件下载和外部链接重定向
- 流式下载，提高下载效率
- 自动检测文件类型，设置正确的MIME类型
- 下载日志记录，包括IP、用户代理等信息
- **数字化下载URL支持**：下载API已更新支持新的数字化字段结构（`download_url_1` 到 `download_url_5`）
- **智能URL选择**：支持通过 `url_index` 参数指定下载URL，如果指定URL为空则自动查找第一个可用的下载URL
- **向后兼容**：保持对旧字段结构的兼容性支持
- **完整URL复制**：复制链接功能使用 `frontend_url` 配置生成完整的URL，确保复制的链接可以在任何地方正常访问
- **上传系统更新**：软件上传功能已更新为使用新的数字化字段结构，上传的软件文件会自动更新到 `download_url_1` 字段
- **前端数据同步**：上传成功后前端会自动重新加载当前软件数据，确保界面显示最新的下载URL信息
- **双字段兼容**：前端同时更新新旧字段名，确保向后兼容性

### 5. 下载选项显示顺序

系统采用数字化字段结构，便于扩展和管理。下载选项按照数字顺序显示：

#### 数字化字段结构
- `download_url_1` - `download_url_5`：5个下载URL字段，便于扩展

#### 默认配置
1. **下载URL 1** (`download_url_1`)：
   - 显示名称：本地下载
   - 图标：`fas fa-download`
   - 描述：从本地服务器下载最新版本
   - 特殊处理：使用getDownloadUrl()方法处理

2. **下载URL 2** (`download_url_2`)：
   - 显示名称：备用下载
   - 图标：`fas fa-cloud-download-alt`
   - 描述：备用下载服务器，网络不佳时推荐

3. **下载URL 3** (`download_url_3`)：
   - 显示名称：百度网盘
   - 图标：`fab fa-baidu`
   - 描述：适合大文件下载，需登录百度账号

4. **下载URL 4** (`download_url_4`)：
   - 显示名称：下载方式4
   - 图标：`fas fa-link`
   - 描述：扩展下载方式
   - 状态：预留扩展

5. **下载URL 5** (`download_url_5`)：
   - 显示名称：下载方式5
   - 图标：`fas fa-external-link-alt`
   - 描述：扩展下载方式
   - 状态：预留扩展

#### 兼容性处理
系统同时支持新旧字段格式：
- API返回时会同时提供新字段（`download_url_1`等）和旧字段（`download_url`等）
- 前端优先使用新字段，如果没有则回退到旧字段
- 后台管理支持新旧字段的混合输入

#### 扩展性
- 可以通过修改`getDownloadConfig()`方法来自定义每个数字对应的下载类型
- 便于后期添加更多下载源类型
- 统一的循环处理逻辑，易于维护

#### 可配置图标系统
系统支持通过配置文件自定义每个下载选项的图标、名称和描述：

**配置方式**：
1. 在 `site_settings.php` 中添加 `download` 配置节
2. 通过 `window.downloadConfig` 全局变量传递配置到前端
3. 前端自动合并默认配置和自定义配置

**配置结构**：
```php
'download' => [
    'url_1' => [
        'name' => '本地下载',
        'icon' => 'fas fa-download',
        'description' => '从本地服务器下载最新版本',
        'useDownloadUrl' => true
    ],
    'url_2' => [
        'name' => '高速下载',
        'icon' => 'fas fa-rocket',
        'description' => '高速下载服务器',
        'useDownloadUrl' => false
    ],
    // ... 其他配置
]
```

**支持的配置项**：
- `name`: 显示名称
- `icon`: FontAwesome 图标类名
- `description`: 描述文字
- `useDownloadUrl`: 是否使用系统下载URL生成逻辑

**常用图标参考**：
- `fas fa-download` - 下载
- `fas fa-server` - 服务器
- `fas fa-cloud` - 云存储
- `fas fa-rocket` - 高速
- `fab fa-github` - GitHub
- `fab fa-baidu` - 百度
- `fas fa-external-link-alt` - 外部链接

这种设计允许管理员根据实际使用的下载源类型来自定义图标和描述，提供更好的用户体验。
