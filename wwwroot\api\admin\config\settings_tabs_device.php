<?php
/**
 * 管理后台设备跳转设置选项卡配置
 *
 * 该文件定义了管理后台设备跳转设置页面的选项卡、分组和字段结构
 * 仅负责UI展示和交互，不包含具体参数的默认值
 */

return [
    // 设备跳转设置选项卡
    [
        'name' => 'device',
        'title' => '设备跳转',
        'icon' => 'fas fa-mobile-alt',
        'groups' => [
            // 设备跳转设置组
            [
                'name' => 'device_redirect_settings',
                'title' => '设备跳转设置',
                'settings_path' => 'device_redirect', // 对应Settings类中的路径
                'fields' => [
                    [
                        'name' => 'device_redirect.enabled',
                        'label' => '启用设备跳转',
                        'type' => 'checkbox',
                        'description' => '启用后将根据用户设备类型自动跳转到指定网址'
                    ]
                ]
            ],
            // iOS设备跳转组
            [
                'name' => 'ios_redirect',
                'title' => 'iOS设备跳转',
                'settings_path' => 'device_redirect',
                'fields' => [
                    [
                        'name' => 'device_redirect.ios_phone',
                        'label' => 'iPhone跳转网址',
                        'type' => 'text',
                        'condition' => 'device_redirect.enabled',
                        'placeholder' => 'https://example.com/ios-phone',
                        'description' => 'iPhone用户访问时跳转的网址，留空则不跳转'
                    ],
                    [
                        'name' => 'device_redirect.ios_tablet',
                        'label' => 'iPad跳转网址',
                        'type' => 'text',
                        'condition' => 'device_redirect.enabled',
                        'placeholder' => 'https://example.com/ios-tablet',
                        'description' => 'iPad用户访问时跳转的网址，留空则不跳转'
                    ]
                ]
            ],
            // Android设备跳转组
            [
                'name' => 'android_redirect',
                'title' => 'Android设备跳转',
                'settings_path' => 'device_redirect',
                'fields' => [
                    [
                        'name' => 'device_redirect.android_phone',
                        'label' => 'Android手机跳转网址',
                        'type' => 'text',
                        'condition' => 'device_redirect.enabled',
                        'placeholder' => 'https://example.com/android-phone',
                        'description' => 'Android手机用户访问时跳转的网址，留空则不跳转'
                    ],
                    [
                        'name' => 'device_redirect.android_tablet',
                        'label' => 'Android平板跳转网址',
                        'type' => 'text',
                        'condition' => 'device_redirect.enabled',
                        'placeholder' => 'https://example.com/android-tablet',
                        'description' => 'Android平板用户访问时跳转的网址，留空则不跳转'
                    ],
                    [
                        'name' => 'device_redirect.android_desktop',
                        'label' => 'Android桌面跳转网址',
                        'type' => 'text',
                        'condition' => 'device_redirect.enabled',
                        'placeholder' => 'https://example.com/android-desktop',
                        'description' => 'Android桌面环境用户访问时跳转的网址，留空则不跳转'
                    ]
                ]
            ],
            // Windows设备跳转组
            [
                'name' => 'windows_redirect',
                'title' => 'Windows设备跳转',
                'settings_path' => 'device_redirect',
                'fields' => [
                    [
                        'name' => 'device_redirect.windows_phone',
                        'label' => 'Windows Phone跳转网址',
                        'type' => 'text',
                        'condition' => 'device_redirect.enabled',
                        'placeholder' => 'https://example.com/windows-phone',
                        'description' => 'Windows Phone用户访问时跳转的网址，留空则不跳转'
                    ],
                    [
                        'name' => 'device_redirect.windows_tablet',
                        'label' => 'Windows平板跳转网址',
                        'type' => 'text',
                        'condition' => 'device_redirect.enabled',
                        'placeholder' => 'https://example.com/windows-tablet',
                        'description' => 'Windows平板用户访问时跳转的网址，留空则不跳转'
                    ],
                    [
                        'name' => 'device_redirect.windows_desktop',
                        'label' => 'Windows桌面跳转网址',
                        'type' => 'text',
                        'condition' => 'device_redirect.enabled',
                        'placeholder' => 'https://example.com/windows-desktop',
                        'description' => 'Windows桌面用户访问时跳转的网址，留空则不跳转'
                    ]
                ]
            ],
            // 其他设备跳转组
            [
                'name' => 'other_redirect',
                'title' => '其他设备跳转',
                'settings_path' => 'device_redirect',
                'fields' => [
                    [
                        'name' => 'device_redirect.macos_desktop',
                        'label' => 'macOS桌面跳转网址',
                        'type' => 'text',
                        'condition' => 'device_redirect.enabled',
                        'placeholder' => 'https://example.com/macos-desktop',
                        'description' => 'macOS桌面用户访问时跳转的网址，留空则不跳转'
                    ],
                    [
                        'name' => 'device_redirect.linux_desktop',
                        'label' => 'Linux桌面跳转网址',
                        'type' => 'text',
                        'condition' => 'device_redirect.enabled',
                        'placeholder' => 'https://example.com/linux-desktop',
                        'description' => 'Linux桌面用户访问时跳转的网址，留空则不跳转'
                    ],
                    [
                        'name' => 'device_redirect.unknown',
                        'label' => '未知设备跳转网址',
                        'type' => 'text',
                        'condition' => 'device_redirect.enabled',
                        'placeholder' => 'https://example.com/unknown-device',
                        'description' => '无法识别的设备类型访问时跳转的网址，留空则不跳转'
                    ]
                ]
            ]
        ]
    ]
];
